{ "pid": 34500, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 34500, "tid": 1, "ts": 1755479375942762, "dur": 3213, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 34500, "tid": 1, "ts": 1755479375945980, "dur": 186850, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 34500, "tid": 1, "ts": 1755479376132832, "dur": 28737, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 34500, "tid": 16971, "ts": 1755479381545772, "dur": 12, "ph": "X", "name": "", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479375942723, "dur": 66652, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376009376, "dur": 5535296, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376009386, "dur": 35, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376009423, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376009425, "dur": 853, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376010282, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376010285, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376010310, "dur": 6, "ph": "X", "name": "ProcessMessages 41", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376010317, "dur": 6946, "ph": "X", "name": "ReadAsync 41", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376017269, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376017272, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376017319, "dur": 1, "ph": "X", "name": "ProcessMessages 201", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376017322, "dur": 76, "ph": "X", "name": "ReadAsync 201", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376017400, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376017402, "dur": 1131, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376018537, "dur": 2, "ph": "X", "name": "ProcessMessages 648", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376018540, "dur": 235, "ph": "X", "name": "ReadAsync 648", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376018776, "dur": 12, "ph": "X", "name": "ProcessMessages 14727", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376018791, "dur": 54, "ph": "X", "name": "ReadAsync 14727", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376018851, "dur": 28, "ph": "X", "name": "ReadAsync 1", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376018881, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376018883, "dur": 38, "ph": "X", "name": "ReadAsync 579", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376018927, "dur": 20, "ph": "X", "name": "ReadAsync 1", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376018950, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376018952, "dur": 32, "ph": "X", "name": "ReadAsync 186", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376018988, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376018990, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376019015, "dur": 126, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376019144, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376019146, "dur": 66, "ph": "X", "name": "ReadAsync 1", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376019216, "dur": 4, "ph": "X", "name": "ProcessMessages 1690", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376019221, "dur": 2548, "ph": "X", "name": "ReadAsync 1690", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376021773, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376021776, "dur": 336, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376022115, "dur": 19, "ph": "X", "name": "ProcessMessages 20494", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376022135, "dur": 39, "ph": "X", "name": "ReadAsync 20494", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376022179, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376022182, "dur": 1930, "ph": "X", "name": "ReadAsync 492", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376024115, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376024118, "dur": 1024, "ph": "X", "name": "ReadAsync 495", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376025145, "dur": 19, "ph": "X", "name": "ProcessMessages 20528", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376025166, "dur": 108, "ph": "X", "name": "ReadAsync 20528", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376025275, "dur": 6, "ph": "X", "name": "ProcessMessages 6857", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376025282, "dur": 2037, "ph": "X", "name": "ReadAsync 6857", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376027323, "dur": 2, "ph": "X", "name": "ProcessMessages 335", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376027327, "dur": 280, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029077, "dur": 38, "ph": "X", "name": "ProcessMessages 20486", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029118, "dur": 480, "ph": "X", "name": "ReadAsync 20486", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029601, "dur": 20, "ph": "X", "name": "ProcessMessages 20481", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029622, "dur": 102, "ph": "X", "name": "ReadAsync 20481", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029727, "dur": 3, "ph": "X", "name": "ProcessMessages 3050", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029732, "dur": 32, "ph": "X", "name": "ReadAsync 3050", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029768, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029770, "dur": 36, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029810, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029813, "dur": 48, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029863, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029866, "dur": 25, "ph": "X", "name": "ReadAsync 540", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029894, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376029896, "dur": 360, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030261, "dur": 39, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030305, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030355, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030357, "dur": 28, "ph": "X", "name": "ReadAsync 509", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030389, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030392, "dur": 33, "ph": "X", "name": "ReadAsync 341", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030427, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030429, "dur": 26, "ph": "X", "name": "ReadAsync 591", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030458, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030460, "dur": 35, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030499, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030501, "dur": 31, "ph": "X", "name": "ReadAsync 563", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030535, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030538, "dur": 33, "ph": "X", "name": "ReadAsync 381", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030574, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030576, "dur": 27, "ph": "X", "name": "ReadAsync 581", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030606, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030609, "dur": 26, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030637, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030639, "dur": 26, "ph": "X", "name": "ReadAsync 316", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030668, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030672, "dur": 39, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030715, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030717, "dur": 37, "ph": "X", "name": "ReadAsync 505", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030756, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030759, "dur": 33, "ph": "X", "name": "ReadAsync 597", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030795, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030798, "dur": 31, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030831, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030832, "dur": 103, "ph": "X", "name": "ReadAsync 499", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030939, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030941, "dur": 53, "ph": "X", "name": "ReadAsync 337", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376030997, "dur": 2, "ph": "X", "name": "ProcessMessages 1811", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376031001, "dur": 29, "ph": "X", "name": "ReadAsync 1811", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376031033, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376031036, "dur": 2005, "ph": "X", "name": "ReadAsync 392", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376033044, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376033047, "dur": 164, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376033213, "dur": 7, "ph": "X", "name": "ProcessMessages 7747", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376034197, "dur": 273, "ph": "X", "name": "ReadAsync 7747", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376034473, "dur": 14, "ph": "X", "name": "ProcessMessages 16523", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376034489, "dur": 37, "ph": "X", "name": "ReadAsync 16523", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376034529, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376034531, "dur": 96, "ph": "X", "name": "ReadAsync 745", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376034632, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376034636, "dur": 32, "ph": "X", "name": "ReadAsync 399", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376034671, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376034674, "dur": 88, "ph": "X", "name": "ReadAsync 792", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376034768, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376034801, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376034804, "dur": 159, "ph": "X", "name": "ReadAsync 470", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376034968, "dur": 60, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035049, "dur": 2, "ph": "X", "name": "ProcessMessages 1575", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035053, "dur": 26, "ph": "X", "name": "ReadAsync 1575", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035082, "dur": 87, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035174, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035206, "dur": 2, "ph": "X", "name": "ProcessMessages 396", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035210, "dur": 33, "ph": "X", "name": "ReadAsync 396", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035246, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035248, "dur": 37, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035288, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035290, "dur": 36, "ph": "X", "name": "ReadAsync 487", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035329, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035332, "dur": 25, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035360, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035362, "dur": 24, "ph": "X", "name": "ReadAsync 429", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035388, "dur": 3, "ph": "X", "name": "ProcessMessages 159", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035392, "dur": 62, "ph": "X", "name": "ReadAsync 159", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035456, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035458, "dur": 30, "ph": "X", "name": "ReadAsync 143", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035491, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035493, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035555, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035593, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035596, "dur": 28, "ph": "X", "name": "ReadAsync 413", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035626, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035628, "dur": 32, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035663, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035666, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035746, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035773, "dur": 1, "ph": "X", "name": "ProcessMessages 211", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035775, "dur": 69, "ph": "X", "name": "ReadAsync 211", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035846, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035850, "dur": 37, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035890, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035893, "dur": 60, "ph": "X", "name": "ReadAsync 558", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376035957, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036001, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036002, "dur": 25, "ph": "X", "name": "ReadAsync 486", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036032, "dur": 27, "ph": "X", "name": "ReadAsync 231", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036061, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036063, "dur": 95, "ph": "X", "name": "ReadAsync 359", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036162, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036199, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036201, "dur": 29, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036232, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036234, "dur": 24, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036260, "dur": 87, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036352, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036388, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036390, "dur": 27, "ph": "X", "name": "ReadAsync 521", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036420, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036422, "dur": 22, "ph": "X", "name": "ReadAsync 391", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036449, "dur": 56, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036507, "dur": 2, "ph": "X", "name": "ProcessMessages 600", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036510, "dur": 50, "ph": "X", "name": "ReadAsync 600", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036563, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036565, "dur": 28, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036595, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036597, "dur": 27, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036627, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036630, "dur": 97, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036732, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036780, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036783, "dur": 45, "ph": "X", "name": "ReadAsync 489", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036831, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036833, "dur": 38, "ph": "X", "name": "ReadAsync 368", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036874, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036876, "dur": 65, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036946, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036983, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376036985, "dur": 23, "ph": "X", "name": "ReadAsync 512", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037011, "dur": 30, "ph": "X", "name": "ReadAsync 535", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037044, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037047, "dur": 108, "ph": "X", "name": "ReadAsync 67", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037178, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037220, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037222, "dur": 31, "ph": "X", "name": "ReadAsync 786", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037256, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037259, "dur": 33, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037294, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037297, "dur": 66, "ph": "X", "name": "ReadAsync 540", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037365, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037367, "dur": 34, "ph": "X", "name": "ReadAsync 273", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037404, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037406, "dur": 30, "ph": "X", "name": "ReadAsync 550", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037439, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037442, "dur": 94, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037540, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037566, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037568, "dur": 22, "ph": "X", "name": "ReadAsync 470", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037593, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037596, "dur": 27, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037626, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037627, "dur": 85, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376037718, "dur": 1563, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039284, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039287, "dur": 137, "ph": "X", "name": "ReadAsync 467", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039428, "dur": 7, "ph": "X", "name": "ProcessMessages 9197", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039436, "dur": 32, "ph": "X", "name": "ReadAsync 9197", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039471, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039474, "dur": 163, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039642, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039679, "dur": 2, "ph": "X", "name": "ProcessMessages 1085", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039682, "dur": 35, "ph": "X", "name": "ReadAsync 1085", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039722, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039754, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039760, "dur": 21, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039786, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039788, "dur": 19, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039814, "dur": 28, "ph": "X", "name": "ReadAsync 219", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039846, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039848, "dur": 69, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039919, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039922, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039957, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376039959, "dur": 42, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040004, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040008, "dur": 27, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040038, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040040, "dur": 68, "ph": "X", "name": "ReadAsync 257", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040112, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040148, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040150, "dur": 31, "ph": "X", "name": "ReadAsync 470", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040184, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040186, "dur": 106, "ph": "X", "name": "ReadAsync 437", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040295, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040298, "dur": 537, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040839, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040842, "dur": 69, "ph": "X", "name": "ReadAsync 479", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040915, "dur": 4, "ph": "X", "name": "ProcessMessages 4131", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040920, "dur": 70, "ph": "X", "name": "ReadAsync 4131", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040992, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376040994, "dur": 70, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041068, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041105, "dur": 1, "ph": "X", "name": "ProcessMessages 1034", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041109, "dur": 26, "ph": "X", "name": "ReadAsync 1034", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041138, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041140, "dur": 70, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041214, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041251, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041253, "dur": 28, "ph": "X", "name": "ReadAsync 539", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041284, "dur": 1, "ph": "X", "name": "ProcessMessages 219", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041286, "dur": 29, "ph": "X", "name": "ReadAsync 219", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041317, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041319, "dur": 80, "ph": "X", "name": "ReadAsync 501", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041402, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041404, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041440, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041442, "dur": 29, "ph": "X", "name": "ReadAsync 550", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041473, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041476, "dur": 27, "ph": "X", "name": "ReadAsync 523", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041504, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041506, "dur": 83, "ph": "X", "name": "ReadAsync 245", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041595, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041637, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041639, "dur": 26, "ph": "X", "name": "ReadAsync 571", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041668, "dur": 1, "ph": "X", "name": "ProcessMessages 126", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041670, "dur": 29, "ph": "X", "name": "ReadAsync 126", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041701, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041704, "dur": 23, "ph": "X", "name": "ReadAsync 476", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041729, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041732, "dur": 90, "ph": "X", "name": "ReadAsync 139", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041826, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041859, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041861, "dur": 40, "ph": "X", "name": "ReadAsync 415", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041903, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041906, "dur": 26, "ph": "X", "name": "ReadAsync 471", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041934, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376041937, "dur": 74, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042016, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042049, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042051, "dur": 30, "ph": "X", "name": "ReadAsync 501", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042084, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042088, "dur": 27, "ph": "X", "name": "ReadAsync 540", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042118, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042120, "dur": 96, "ph": "X", "name": "ReadAsync 260", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042220, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042254, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042256, "dur": 327, "ph": "X", "name": "ReadAsync 537", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042653, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042656, "dur": 128, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042788, "dur": 3, "ph": "X", "name": "ProcessMessages 2411", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042792, "dur": 113, "ph": "X", "name": "ReadAsync 2411", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376042912, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376043050, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376043069, "dur": 420, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376043493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376043495, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376043514, "dur": 359, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376043879, "dur": 502, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376044384, "dur": 96, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376044483, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376044522, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376044525, "dur": 179, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376044708, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376044740, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376044769, "dur": 240, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045014, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045048, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045086, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045107, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045169, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045199, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045241, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045243, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045289, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045309, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045312, "dur": 223, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045539, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045586, "dur": 6, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045595, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045633, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045635, "dur": 228, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045867, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045885, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376045887, "dur": 137, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046028, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046055, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046085, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046182, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046204, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046242, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046291, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046313, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046316, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046420, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046457, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046479, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046579, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046607, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046609, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046668, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046701, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046704, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046855, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046912, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046914, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046942, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046944, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376046987, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376047051, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376047056, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376047080, "dur": 242, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376047326, "dur": 203, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376047533, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376047535, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376047558, "dur": 127, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376047774, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376047807, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376047809, "dur": 446, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048261, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048340, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048343, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048384, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048386, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048415, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048416, "dur": 172, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048592, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048594, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048623, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048625, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048652, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048682, "dur": 264, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048951, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048981, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376048984, "dur": 408, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376049398, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376049429, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376049522, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376049557, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376049560, "dur": 68, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376049631, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376049635, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376049676, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376049678, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376049807, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376049905, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376049907, "dur": 847, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376050787, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376050790, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376050823, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376050825, "dur": 724, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376051554, "dur": 236, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376051796, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376051860, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376051863, "dur": 40, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376051907, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376051910, "dur": 279, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376052193, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376052284, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376052286, "dur": 93, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376052383, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376052411, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376052412, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376052451, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376052479, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376052639, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376052674, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376052677, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376052745, "dur": 680, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376053429, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376053454, "dur": 523, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376053982, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376054009, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376054012, "dur": 22, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376054037, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376054039, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376054099, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376054135, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376054136, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376054178, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376054197, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376054294, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376054326, "dur": 709, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376055041, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376055075, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376055078, "dur": 1251, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376056335, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376056371, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376056374, "dur": 90, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376056468, "dur": 1323, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376057796, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376057937, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376057940, "dur": 1643, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376059587, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376059589, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376059607, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376059609, "dur": 337, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376059952, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376059998, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376060000, "dur": 19, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376060023, "dur": 617, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376060645, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376060688, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376060690, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376060719, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376060813, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376060815, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376060909, "dur": 67, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376060978, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376061096, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376061099, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376061123, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376061125, "dur": 926, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062057, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062118, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062121, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062234, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062236, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062268, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062270, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062331, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062360, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062441, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062471, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062472, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062608, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062637, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376062640, "dur": 863, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376063507, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376063541, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376063543, "dur": 64, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376063615, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376063646, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376063648, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376063680, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376063765, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376063768, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376063862, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376063864, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376063882, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376063884, "dur": 150, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064039, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064067, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064086, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064141, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064173, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064291, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064310, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064312, "dur": 69, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064387, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064410, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064454, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064476, "dur": 414, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064896, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064970, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376064973, "dur": 53, "ph": "X", "name": "ReadAsync 164", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065028, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065030, "dur": 247, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065280, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065283, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065317, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065320, "dur": 59, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065383, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065430, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065433, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065497, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065591, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065625, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065627, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065685, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065687, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065721, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065723, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065916, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376065919, "dur": 196, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066118, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066120, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066184, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066186, "dur": 109, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066299, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066300, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066340, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066343, "dur": 56, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066401, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066403, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066433, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066515, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066604, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066606, "dur": 165, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066774, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066776, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066824, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066826, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376066893, "dur": 220, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067118, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067155, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067158, "dur": 130, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067292, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067319, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067321, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067338, "dur": 193, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067534, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067536, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067571, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067573, "dur": 129, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067707, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067766, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067805, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067807, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067862, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067864, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067887, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067901, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376067962, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376068036, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376068038, "dur": 834, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376068875, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376068877, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376068902, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376068904, "dur": 61, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376068970, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069004, "dur": 267, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069288, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069321, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069324, "dur": 84, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069412, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069436, "dur": 170, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069608, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069610, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069693, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069825, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069882, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069884, "dur": 38, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069926, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376069928, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376070016, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376070041, "dur": 1145, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376071190, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376071220, "dur": 22, "ph": "X", "name": "ProcessMessages 242", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376071243, "dur": 380, "ph": "X", "name": "ReadAsync 242", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376071628, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376071702, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376071705, "dur": 252, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376071993, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376071996, "dur": 176, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072174, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072176, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072212, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072214, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072241, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072276, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072426, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072458, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072460, "dur": 181, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072646, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072709, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072711, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072742, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072745, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072775, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072803, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072804, "dur": 103, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072912, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072961, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376072963, "dur": 260, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376073228, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376073266, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376073268, "dur": 241, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376073512, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376073514, "dur": 153, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376073670, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376073672, "dur": 30, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376073717, "dur": 18, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376073737, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376073781, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376073783, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376073825, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376073856, "dur": 334, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074195, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074226, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074228, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074246, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074285, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074313, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074428, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074459, "dur": 298, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074761, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074795, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074798, "dur": 25, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074827, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074901, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376074978, "dur": 11, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376075008, "dur": 172, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376075183, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376075186, "dur": 161, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376075359, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376075441, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376075444, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376075503, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376075532, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376075672, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376075704, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376075707, "dur": 150, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376075861, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376075993, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076085, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076226, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076228, "dur": 126, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076359, "dur": 121, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076484, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076514, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076516, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076572, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076600, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076663, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076707, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076709, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076744, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076747, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076767, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076847, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076977, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376076979, "dur": 131, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376077115, "dur": 356, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376077474, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376077476, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376077506, "dur": 5544, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376083054, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376083056, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376083080, "dur": 301, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376083387, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376083420, "dur": 1415, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376084840, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376084876, "dur": 8757, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376093638, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376093648, "dur": 199, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376093850, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376093852, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376093881, "dur": 281, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376094167, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376094222, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376094224, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376094253, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376094255, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376094364, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376094387, "dur": 969, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376095361, "dur": 366, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376095732, "dur": 1478, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376097214, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376097232, "dur": 1522, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376098759, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376098787, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376098790, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376098899, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376098920, "dur": 487, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376099411, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376099431, "dur": 368, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376099804, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376099824, "dur": 321, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376100151, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376100177, "dur": 251, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376100435, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376100466, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376100468, "dur": 472, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376100944, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376100948, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376100991, "dur": 477, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376101471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376101474, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376101508, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376101511, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376101581, "dur": 251, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376101836, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376101872, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376101874, "dur": 933, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376102814, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376102841, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376102843, "dur": 66, "ph": "X", "name": "ReadAsync 164", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376102915, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376102933, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376103009, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376103045, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376103047, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376103130, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376103132, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376103157, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376103160, "dur": 488, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376103653, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376103684, "dur": 17, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376103704, "dur": 149, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376103856, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376103858, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376103916, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376104118, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376104123, "dur": 167, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376104295, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376104324, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376104326, "dur": 611, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376104943, "dur": 220, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376105166, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376105168, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376105188, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376105310, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376105331, "dur": 971, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106307, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106339, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106341, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106476, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106510, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106512, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106628, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106631, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106791, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106818, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106820, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106839, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106984, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376106987, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376107051, "dur": 6407, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376113462, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376113465, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376113503, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376113505, "dur": 4954, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376119055, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376119057, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376119099, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376119102, "dur": 5458, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376124564, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376124567, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376124592, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376124687, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376124704, "dur": 11534, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376136243, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376136246, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376136276, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376136278, "dur": 1112, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376137394, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376137396, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376137430, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376137432, "dur": 1641, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376139078, "dur": 191, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376139291, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376139294, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376139322, "dur": 387, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376139712, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376139714, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376139731, "dur": 1863, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376141598, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376141601, "dur": 1291, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376142896, "dur": 28, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376142926, "dur": 226, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376143156, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376143246, "dur": 234, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376143484, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376143486, "dur": 586, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376144076, "dur": 531, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376144610, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376144612, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376144642, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376144644, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376144759, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376144800, "dur": 301, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376145107, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376145131, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376145288, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376145319, "dur": 704, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376146028, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376146063, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376146087, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376146103, "dur": 360, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376146468, "dur": 265, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376146737, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376146752, "dur": 1141, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376147898, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376147916, "dur": 2213, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376150135, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376150171, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376150201, "dur": 206, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376150412, "dur": 182, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376150597, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376150599, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376150626, "dur": 826, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376151456, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376151518, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376151579, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376151597, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376151644, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376151674, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376151716, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376151745, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376151747, "dur": 117, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376151868, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376151916, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376151918, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376151946, "dur": 662, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376152612, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376152613, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376152658, "dur": 3, "ph": "X", "name": "ProcessMessages 56", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376152664, "dur": 1405, "ph": "X", "name": "ReadAsync 56", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376154074, "dur": 151, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376154231, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376154233, "dur": 164, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376154402, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376154437, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376154439, "dur": 592, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376155036, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376155071, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376155073, "dur": 350, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376155428, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376155463, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376155465, "dur": 6431, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376161902, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376161910, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376161942, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376162200, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376162225, "dur": 180, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376162411, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376162429, "dur": 1474, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376163908, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376163927, "dur": 510, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376164446, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376164448, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376164482, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376164484, "dur": 440, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376164930, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376164963, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376164965, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376165070, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376165152, "dur": 1075, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376166232, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376166266, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376166268, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376166392, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376166429, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479376166432, "dur": 980444, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377146887, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377146892, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377146928, "dur": 20, "ph": "X", "name": "ProcessMessages 188", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377146950, "dur": 49566, "ph": "X", "name": "ReadAsync 188", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377196527, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377196532, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377196564, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377196570, "dur": 333, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377196908, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377196910, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377196975, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377196977, "dur": 1199, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377198181, "dur": 144, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377198367, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377198369, "dur": 1240, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377199614, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377199655, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377199657, "dur": 557, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377200220, "dur": 702, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377200924, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377200942, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377200992, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377201028, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377201030, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377201106, "dur": 140, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377201249, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377201251, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377201272, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377201275, "dur": 1499, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377202779, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377202827, "dur": 4, "ph": "X", "name": "ProcessMessages 140", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377202832, "dur": 54, "ph": "X", "name": "ReadAsync 140", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377202888, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377202891, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377203048, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377203050, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377203102, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377203104, "dur": 268, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377203424, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377203426, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377203472, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377203519, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377203523, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377203656, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377203704, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377203706, "dur": 753, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377204462, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377204464, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377204498, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377204500, "dur": 210, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377204719, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377204736, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377204738, "dur": 836, "ph": "X", "name": "ReadAsync 24", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377205579, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377205610, "dur": 2, "ph": "X", "name": "ProcessMessages 88", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377205614, "dur": 4676, "ph": "X", "name": "ReadAsync 88", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377210294, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377210297, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377210320, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377210342, "dur": 40, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377210385, "dur": 15, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377210401, "dur": 19, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377210423, "dur": 12, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377210437, "dur": 205, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377210647, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377210676, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377210687, "dur": 525, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211218, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211247, "dur": 11, "ph": "X", "name": "ProcessMessages 74", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211260, "dur": 57, "ph": "X", "name": "ReadAsync 74", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211321, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211345, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211347, "dur": 67, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211420, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211453, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211468, "dur": 18, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211488, "dur": 9, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211498, "dur": 18, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211519, "dur": 350, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211874, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211906, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377211916, "dur": 97, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212018, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212062, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212065, "dur": 69, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212139, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212167, "dur": 9, "ph": "X", "name": "ProcessMessages 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212177, "dur": 32, "ph": "X", "name": "ReadAsync 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212212, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212214, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212310, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212342, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212353, "dur": 496, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212855, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212887, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212898, "dur": 26, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212927, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377212930, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377213036, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377213066, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377213068, "dur": 769, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377213844, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377213865, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377213868, "dur": 3235, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377217109, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377217154, "dur": 19, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377217175, "dur": 18, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377217196, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377217199, "dur": 411, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377217615, "dur": 123, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377217742, "dur": 12, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377217755, "dur": 22, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377217779, "dur": 8, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377217789, "dur": 1001, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377218794, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377218825, "dur": 13, "ph": "X", "name": "ProcessMessages 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377218840, "dur": 15, "ph": "X", "name": "ReadAsync 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377218856, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377218858, "dur": 147, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377219011, "dur": 335, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377219363, "dur": 189, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377219558, "dur": 24, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377219585, "dur": 10, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377219596, "dur": 22, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377219621, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377219624, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377219760, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377219795, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377219807, "dur": 479, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377220291, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377220332, "dur": 77, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377220412, "dur": 560, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377220975, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377220977, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377221000, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377221003, "dur": 175, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377221184, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377221206, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377221216, "dur": 304, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377221525, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377221549, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377221552, "dur": 572, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377222128, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377222182, "dur": 10, "ph": "X", "name": "ProcessMessages 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377222193, "dur": 25, "ph": "X", "name": "ReadAsync 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377222222, "dur": 10, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377222233, "dur": 616, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377222853, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377222884, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377222887, "dur": 396, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377223288, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377223323, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377223326, "dur": 503, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377223833, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377223891, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377223903, "dur": 250, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377224159, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377224183, "dur": 8, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377224192, "dur": 21, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377224224, "dur": 1076, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377225304, "dur": 46, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377225354, "dur": 16, "ph": "X", "name": "ProcessMessages 152", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377225371, "dur": 991, "ph": "X", "name": "ReadAsync 152", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377226367, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377226390, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377226392, "dur": 37, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377226432, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377226435, "dur": 826, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377227266, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377227289, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377227300, "dur": 628, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377227932, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377227977, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377227993, "dur": 116, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377228114, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377228145, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377228148, "dur": 428, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377228582, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377228609, "dur": 8, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377228618, "dur": 376, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377228999, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377229048, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377229051, "dur": 81, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377229135, "dur": 10, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377229147, "dur": 18, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377229167, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377229169, "dur": 260, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377229432, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377229434, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377229459, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377229469, "dur": 267, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377229742, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377229758, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377229769, "dur": 369, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377230144, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377230176, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377230179, "dur": 55, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377230239, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377230267, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377230270, "dur": 459, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377230733, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377230819, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377230822, "dur": 30, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377230855, "dur": 20, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377230878, "dur": 260, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377231143, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377231173, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377231186, "dur": 577, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377231768, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377231800, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377231802, "dur": 114, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377231922, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377231963, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377231969, "dur": 200, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377232174, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377232203, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377232206, "dur": 968, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377233177, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377233179, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377233214, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377233226, "dur": 179, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377233419, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377233452, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377233463, "dur": 1137, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377234605, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377234666, "dur": 11, "ph": "X", "name": "ProcessMessages 74", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377234678, "dur": 690, "ph": "X", "name": "ReadAsync 74", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377235373, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377235401, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377235413, "dur": 333, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377235755, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377235757, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377235792, "dur": 19, "ph": "X", "name": "ProcessMessages 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377235814, "dur": 424, "ph": "X", "name": "ReadAsync 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377236244, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377236280, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377236283, "dur": 176, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377236464, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377236494, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377236497, "dur": 118, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377236619, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377236653, "dur": 16, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377236671, "dur": 89, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377236765, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377236794, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377236814, "dur": 831, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377237650, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377237683, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377237686, "dur": 60, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377237751, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377237796, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377237798, "dur": 81, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377237898, "dur": 22, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377237922, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377237972, "dur": 21, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377237995, "dur": 453, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377238453, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377238491, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377238501, "dur": 124, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377238630, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377238649, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377238651, "dur": 59, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377238713, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377238716, "dur": 641, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377239362, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377239393, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377239396, "dur": 547, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377239948, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377239979, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377239995, "dur": 400, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377240400, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377240429, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377240441, "dur": 759, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377241203, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377241206, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377241238, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377241241, "dur": 468, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377241715, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377241745, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377241748, "dur": 424, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377242177, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377242195, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377242205, "dur": 189, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377242399, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377242443, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377242453, "dur": 637, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243098, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243125, "dur": 9, "ph": "X", "name": "ProcessMessages 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243136, "dur": 157, "ph": "X", "name": "ReadAsync 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243298, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243315, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243318, "dur": 325, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243648, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243678, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243689, "dur": 102, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243796, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243816, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243819, "dur": 45, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243867, "dur": 9, "ph": "X", "name": "ProcessMessages 91", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243878, "dur": 23, "ph": "X", "name": "ReadAsync 91", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243947, "dur": 10, "ph": "X", "name": "ProcessMessages 1", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377243959, "dur": 282, "ph": "X", "name": "ReadAsync 1", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377244246, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377244276, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377244279, "dur": 462, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377244746, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377244780, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377244784, "dur": 121, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377244909, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377244940, "dur": 26, "ph": "X", "name": "ProcessMessages 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377244968, "dur": 125, "ph": "X", "name": "ReadAsync 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377245096, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377245130, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377245141, "dur": 336, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377245483, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377245502, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377245504, "dur": 142, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377245651, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377245742, "dur": 11, "ph": "X", "name": "ProcessMessages 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377245755, "dur": 1517, "ph": "X", "name": "ReadAsync 54", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377247277, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377247307, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377247319, "dur": 1030, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377248354, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377248383, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377248396, "dur": 905, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377249306, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377249364, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377249377, "dur": 26, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377249406, "dur": 16, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377249423, "dur": 356, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377249784, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377249828, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377249840, "dur": 90, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377249936, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377249966, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377249977, "dur": 4001, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377253987, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377253990, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377254025, "dur": 24, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377254052, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377254109, "dur": 214, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377254327, "dur": 98, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377254427, "dur": 128302, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377382739, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377382744, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377382803, "dur": 31, "ph": "X", "name": "ReadAsync 49794", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377382836, "dur": 21, "ph": "X", "name": "ProcessMessages 15096", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377382859, "dur": 355322, "ph": "X", "name": "ReadAsync 15096", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377738192, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377738196, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377738237, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479377738241, "dur": 1004594, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479378742845, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479378742849, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479378742884, "dur": 26, "ph": "X", "name": "ProcessMessages 499", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479378742912, "dur": 114393, "ph": "X", "name": "ReadAsync 499", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479378857315, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479378857320, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479378857352, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479378857356, "dur": 2323, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479378859684, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479378859687, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479378859715, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479378859733, "dur": 232542, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479379092284, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479379092291, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479379092325, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479379092329, "dur": 2830, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479379095165, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479379095168, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479379095248, "dur": 20, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479379095270, "dur": 1052558, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479380147838, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479380147843, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479380147879, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479380147884, "dur": 1379184, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479381527077, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479381527081, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479381527118, "dur": 23, "ph": "X", "name": "ProcessMessages 5949", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479381527143, "dur": 899, "ph": "X", "name": "ReadAsync 5949", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479381528047, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479381528085, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479381528090, "dur": 548, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479381528643, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 25769803776, "ts": 1755479381528670, "dur": 15993, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 34500, "tid": 16971, "ts": 1755479381545786, "dur": 2615, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 34500, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 34500, "tid": 21474836480, "ts": 1755479375942681, "dur": 218906, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 34500, "tid": 21474836480, "ts": 1755479376161589, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 34500, "tid": 21474836480, "ts": 1755479376161590, "dur": 47, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 34500, "tid": 16971, "ts": 1755479381548403, "dur": 30, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 34500, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 34500, "tid": 17179869184, "ts": 1755479375935055, "dur": 5609652, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 34500, "tid": 17179869184, "ts": 1755479375935179, "dur": 7465, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 34500, "tid": 17179869184, "ts": 1755479381544722, "dur": 64, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 34500, "tid": 17179869184, "ts": 1755479381544738, "dur": 14, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 34500, "tid": 17179869184, "ts": 1755479381544788, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 34500, "tid": 16971, "ts": 1755479381548436, "dur": 11, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1755479376011121, "dur":3798, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479376014929, "dur":1163, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479376016299, "dur":96, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1755479376016395, "dur":2510, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479376018990, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ScriptAssemblies" }}
,{ "pid":12345, "tid":0, "ts":1755479376019126, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_49CBFF92A2275F6F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1755479376020276, "dur":286, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_CEFDB7E99B7CF7EA.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1755479376020586, "dur":76, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_4F62A7EF774B61E6.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1755479376020723, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_650A7E6E6CF77814.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1755479376020885, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AB4CD292DE0C13CA.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1755479376022073, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1755479376022164, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.Timeline.rsp" }}
,{ "pid":12345, "tid":0, "ts":1755479376022798, "dur":203, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Collections.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1755479376023494, "dur":356, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1755479376024061, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll_A11292DE152BD14A.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1755479376025792, "dur":360, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.Formats.Fbx.Runtime.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1755479376026900, "dur":111, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Recorder.Base.ref.dll_63987A53FC4131C4.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1755479376028718, "dur":639, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_497B5BD213CB63CC.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1755479376030713, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_2D1A3CE81E2A30A9.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1755479376030791, "dur":365, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_E425C80947DCA283.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1755479376031365, "dur":93, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_19C4E7C373307C98.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1755479376031611, "dur":427, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A7A2D902ABB91631.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1755479376034156, "dur":118, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Mathematics.rsp" }}
,{ "pid":12345, "tid":0, "ts":1755479376034376, "dur":85, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Mathematics.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1755479376034829, "dur":131, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1755479376036031, "dur":175, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/BakeryRuntimeAssembly.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1755479376039462, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Recorder.Base.dll (+pdb)" }}
,{ "pid":12345, "tid":0, "ts":1755479376040199, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/16165950076103467137.rsp" }}
,{ "pid":12345, "tid":0, "ts":1755479376041048, "dur":115, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/10262047607611578282.rsp" }}
,{ "pid":12345, "tid":0, "ts":1755479376042593, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/4007711799635523933.rsp" }}
,{ "pid":12345, "tid":0, "ts":1755479376018947, "dur":25402, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479376044365, "dur":5485415, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479381529781, "dur":241, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479381530229, "dur":87, "ph":"X", "name": "BuildQueueDestroyTail",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479381530361, "dur":3376, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1755479376018876, "dur":25496, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376044417, "dur":260, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376044683, "dur":612, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3A9699D5B61D14E5.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376045296, "dur":403, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376045704, "dur":631, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_C4C474C0FA62BD97.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376046336, "dur":490, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376046830, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_4F412B6126057451.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376047068, "dur":231, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376047304, "dur":285, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_07FF3F3DDF33725E.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376047590, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376047786, "dur":332, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_25B50183F186EBBA.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376048118, "dur":476, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376048598, "dur":223, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_CF990ED46D3DE6D9.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376048822, "dur":210, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376049039, "dur":406, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_84478F8B74AB594B.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376049446, "dur":674, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376050124, "dur":459, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_900A8EA65EC885E3.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376050583, "dur":768, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376051356, "dur":910, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_254CDD99FB5B4312.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376052267, "dur":1085, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376053358, "dur":535, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_D4195B54B04454A5.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376053894, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376054114, "dur":427, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_1ACD155A8BE839AF.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376054541, "dur":1046, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376055592, "dur":154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2A5CE0F0450FED37.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376055747, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376055901, "dur":426, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_4F62A7EF774B61E6.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376056328, "dur":872, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376057206, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_0E816B21A5425FDB.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376057328, "dur":1113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376058454, "dur":2588, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_C0841F504AB497D4.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376061044, "dur":576, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376061626, "dur":566, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_A5BE45DEE44577F5.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376062193, "dur":408, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376062644, "dur":399, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_F02C8306DB78C334.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376063044, "dur":885, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376063935, "dur":692, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_407B180FBB16958F.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376064627, "dur":749, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376065381, "dur":266, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_EA35D255DFD0CCA1.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376065648, "dur":328, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376065981, "dur":160, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_9B5EB9868E4A54EF.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376066142, "dur":299, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376066455, "dur":470, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376066939, "dur":381, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376067326, "dur":388, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376067719, "dur":413, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A5FBD79E52C0F38C.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376068132, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376068250, "dur":550, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376068806, "dur":415, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376069233, "dur":443, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376069734, "dur":308, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376070048, "dur":1645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376071701, "dur":353, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376072064, "dur":1105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376073175, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376073751, "dur":514, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376074294, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376074926, "dur":273, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376075206, "dur":774, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376075987, "dur":715, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376076752, "dur":463, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376077240, "dur":556, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376077820, "dur":464, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376078311, "dur":268, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376079997, "dur":560, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Core\\VoidRescueSystem.cs" }}
,{ "pid":12345, "tid":1, "ts":1755479376081683, "dur":1259, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\AudioSystems\\SettingsAudioManager.cs" }}
,{ "pid":12345, "tid":1, "ts":1755479376078584, "dur":4934, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376083519, "dur":3386, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376088998, "dur":505, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479376089991, "dur":502, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479376086906, "dur":7332, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376094239, "dur":613, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Ini.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479376096415, "dur":506, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Binder.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479376099840, "dur":507, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479376094238, "dur":6822, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376101061, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376101130, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376101350, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Multiplayer.Center.Common.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376101426, "dur":304, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376101759, "dur":1032, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Multiplayer.Center.Common.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1755479376102792, "dur":949, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376103746, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Domain_Reload.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376103821, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376103967, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376104094, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376104223, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376104349, "dur":309, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Tayx.Graphy.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1755479376104659, "dur":494, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376105158, "dur":342, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Csg.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1755479376105501, "dur":1130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376107334, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\clrjit.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479376110407, "dur":1010, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-crt-process-l1-1-0.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479376106636, "dur":7092, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376113729, "dur":2526, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376116256, "dur":676, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479376117281, "dur":3030, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479376120577, "dur":636, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479376122213, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479376122955, "dur":1106, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479376124061, "dur":672, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479376116255, "dur":10177, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376126705, "dur":898, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@c19679f07bae\\Runtime\\Shapes\\Cone.cs" }}
,{ "pid":12345, "tid":1, "ts":1755479376128234, "dur":1490, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@c19679f07bae\\Runtime\\MeshOperations\\SurfaceTopology.cs" }}
,{ "pid":12345, "tid":1, "ts":1755479376126436, "dur":4732, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376131169, "dur":2615, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376134867, "dur":4052, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Settings\\DiffusionProfileDefaultSettings.cs" }}
,{ "pid":12345, "tid":1, "ts":1755479376139783, "dur":696, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\WorldLightManager.cs" }}
,{ "pid":12345, "tid":1, "ts":1755479376133784, "dur":6967, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376141013, "dur":766, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Lighting\\VolumetricLighting\\HDRenderPipeline.VolumetricLighting.cs" }}
,{ "pid":12345, "tid":1, "ts":1755479376143201, "dur":2347, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Lighting\\VolumetricClouds\\HDRenderPipeline.VolumetricCloudsFullResolution.cs" }}
,{ "pid":12345, "tid":1, "ts":1755479376140752, "dur":5941, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376146693, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@bd0e8186c2bc\\Runtime\\Utilities\\CoreMatrixUtils.cs" }}
,{ "pid":12345, "tid":1, "ts":1755479376146693, "dur":2738, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376149431, "dur":2598, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376152030, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1755479376152111, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376152230, "dur":416, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1755479376152647, "dur":517, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376153176, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376153299, "dur":3268, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376156714, "dur":882, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstanceActionInvoker_1.cs" }}
,{ "pid":12345, "tid":1, "ts":1755479376156568, "dur":3831, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479376160399, "dur":1034999, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479377195399, "dur":1676, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1755479377197076, "dur":1510, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479377198592, "dur":1377, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/UnityEngine.UI.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1755479377199974, "dur":2731, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479377202713, "dur":1479, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1755479377204193, "dur":851, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479377205055, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479377205217, "dur":7727, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479377212949, "dur":729, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479377213717, "dur":7929, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479377221652, "dur":784, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479377222448, "dur":6545, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479377228998, "dur":818, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479377229832, "dur":6480, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Formats.Fbx.Runtime.pdb" }}
,{ "pid":12345, "tid":1, "ts":1755479377236317, "dur":1176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479377237506, "dur":6412, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProGrids.pdb" }}
,{ "pid":12345, "tid":1, "ts":1755479377243923, "dur":901, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479377244848, "dur":6243, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Tayx.Graphy.pdb" }}
,{ "pid":12345, "tid":1, "ts":1755479377251093, "dur":4278746, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376048536, "dur":15059, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E2C42E2935718C4D.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1755479376063596, "dur":21160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376084769, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1755479376084949, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376085098, "dur":52484, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1755479376137584, "dur":339, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376137945, "dur":486, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376138461, "dur":286, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1755479376138748, "dur":200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376138953, "dur":1231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1755479376140185, "dur":600, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376140804, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376140929, "dur":371, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1755479376141301, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376141430, "dur":607, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEditor.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1755479376142037, "dur":1271, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376143325, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376143434, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1755479376143535, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376143640, "dur":388, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1755479376144029, "dur":1452, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376145497, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376145613, "dur":119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1755479376145733, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376145850, "dur":400, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Burst.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1755479376146250, "dur":745, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376147084, "dur":1180, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376148265, "dur":796, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Nesting\\GraphOutput.cs" }}
,{ "pid":12345, "tid":2, "ts":1755479376148264, "dur":3545, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376153475, "dur":580, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnInputFieldValueChanged.cs" }}
,{ "pid":12345, "tid":2, "ts":1755479376154056, "dur":1244, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnInputFieldEndEdit.cs" }}
,{ "pid":12345, "tid":2, "ts":1755479376151809, "dur":4687, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376156669, "dur":940, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\ReflectionInvoker.cs" }}
,{ "pid":12345, "tid":2, "ts":1755479376156496, "dur":4854, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479376161350, "dur":1034032, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479377195384, "dur":1691, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Burst.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1755479377197076, "dur":2791, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479377199876, "dur":1331, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.ProBuilder.KdTree.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1755479377201208, "dur":1679, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479377202893, "dur":2169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Collections.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1755479377205063, "dur":1353, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479377206427, "dur":798, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479377207241, "dur":7342, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" }}
,{ "pid":12345, "tid":2, "ts":1755479377214589, "dur":891, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479377215496, "dur":7414, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":2, "ts":1755479377222916, "dur":809, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479377223769, "dur":7829, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":2, "ts":1755479377231612, "dur":824, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479377232450, "dur":6045, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Domain_Reload.pdb" }}
,{ "pid":12345, "tid":2, "ts":1755479377238500, "dur":954, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479377239469, "dur":6124, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Csg.pdb" }}
,{ "pid":12345, "tid":2, "ts":1755479377245597, "dur":945, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755479377246559, "dur":9194, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Runtime.pdb" }}
,{ "pid":12345, "tid":2, "ts":1755479377255755, "dur":4274044, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376048571, "dur":716, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_CF9F9FD71901DE7D.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1755479376049288, "dur":735, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376050030, "dur":502, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_A91F79AA39514C97.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1755479376050533, "dur":686, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376051224, "dur":849, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_00A05AD5B200358E.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1755479376052074, "dur":1267, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376053347, "dur":770, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_222CD44E8B7C5082.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1755479376054118, "dur":282, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376054405, "dur":619, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_E3AF384A382B3337.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1755479376055025, "dur":678, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376055708, "dur":739, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_49CBFF92A2275F6F.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1755479376056448, "dur":1271, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376057725, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_704251132F2E7A4E.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1755479376057878, "dur":712, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376058596, "dur":2537, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_BC45BDE847885BDD.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1755479376061133, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376061699, "dur":1089, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_A4CEBA1304CFC592.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1755479376062788, "dur":1501, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376064297, "dur":796, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_4DAFA27FA661967D.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1755479376065093, "dur":636, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376065733, "dur":357, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1755479376066091, "dur":969, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376067071, "dur":372, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376067511, "dur":374, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376067901, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376068043, "dur":443, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376068495, "dur":527, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376069034, "dur":512, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376069551, "dur":1132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376070699, "dur":802, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376071506, "dur":434, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376071954, "dur":1327, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376073306, "dur":514, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376073834, "dur":494, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376074404, "dur":897, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376075301, "dur":241, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.Multiplayer.Center.Common.rsp" }}
,{ "pid":12345, "tid":3, "ts":1755479376075550, "dur":841, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376076399, "dur":432, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376076856, "dur":590, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376077469, "dur":599, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376078075, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/16791050694966752453.rsp" }}
,{ "pid":12345, "tid":3, "ts":1755479376078132, "dur":603, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376080982, "dur":17250, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@c19679f07bae\\External\\CSG\\Classes\\Node.cs" }}
,{ "pid":12345, "tid":3, "ts":1755479376078741, "dur":19959, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376098701, "dur":71, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1755479376098773, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376098895, "dur":9078, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1755479376107974, "dur":588, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376108572, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376108701, "dur":2783, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376113774, "dur":654, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\TestRun\\Tasks\\PostbuildCleanupTask.cs" }}
,{ "pid":12345, "tid":3, "ts":1755479376116224, "dur":1094, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\TestRun\\Tasks\\PerformUndoTask.cs" }}
,{ "pid":12345, "tid":3, "ts":1755479376111485, "dur":5833, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376117319, "dur":2894, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":3, "ts":1755479376120589, "dur":1131, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":3, "ts":1755479376122068, "dur":846, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":3, "ts":1755479376122914, "dur":4028, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll" }}
,{ "pid":12345, "tid":3, "ts":1755479376127224, "dur":9957, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll" }}
,{ "pid":12345, "tid":3, "ts":1755479376138566, "dur":640, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":3, "ts":1755479376117319, "dur":22005, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376140396, "dur":525, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Material\\Eye\\Eye.cs" }}
,{ "pid":12345, "tid":3, "ts":1755479376141818, "dur":795, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Material\\DecalMeshBiasTypeEnum.cs" }}
,{ "pid":12345, "tid":3, "ts":1755479376143394, "dur":1174, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Material\\Decal\\DecalProjector.Migration.cs" }}
,{ "pid":12345, "tid":3, "ts":1755479376145491, "dur":506, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Material\\AxF\\AxFAPI.cs" }}
,{ "pid":12345, "tid":3, "ts":1755479376139325, "dur":7183, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376146641, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@bd0e8186c2bc\\Runtime\\Utilities\\SceneRenderPipeline.cs" }}
,{ "pid":12345, "tid":3, "ts":1755479376148436, "dur":730, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@bd0e8186c2bc\\Runtime\\Utilities\\HDROutputUtils.cs" }}
,{ "pid":12345, "tid":3, "ts":1755479376149592, "dur":767, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@bd0e8186c2bc\\Runtime\\Utilities\\HaltonSequence.cs" }}
,{ "pid":12345, "tid":3, "ts":1755479376146508, "dur":4695, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376151203, "dur":2868, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376154564, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Variables\\Variables.cs" }}
,{ "pid":12345, "tid":3, "ts":1755479376154103, "dur":3371, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376157475, "dur":3141, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479376160617, "dur":1035004, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479377195623, "dur":1700, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.Core.Samples.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1755479377197324, "dur":2655, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479377199984, "dur":1318, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1755479377201302, "dur":1782, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479377203092, "dur":1376, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1755479377204469, "dur":947, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479377205445, "dur":1287, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479377206742, "dur":7147, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Domain_Reload.dll" }}
,{ "pid":12345, "tid":3, "ts":1755479377213894, "dur":711, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479377214616, "dur":7108, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":3, "ts":1755479377221729, "dur":1500, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479377223243, "dur":6302, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" }}
,{ "pid":12345, "tid":3, "ts":1755479377229560, "dur":1141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479377237023, "dur":81, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479377230726, "dur":6386, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb" }}
,{ "pid":12345, "tid":3, "ts":1755479377237118, "dur":828, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479377237959, "dur":6161, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":3, "ts":1755479377244125, "dur":824, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479377245001, "dur":6033, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.pdb" }}
,{ "pid":12345, "tid":3, "ts":1755479377251035, "dur":4278742, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376018901, "dur":25485, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376044410, "dur":208, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376044633, "dur":641, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_99EE7CEBB57AD042.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376045275, "dur":330, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376045615, "dur":348, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B9A32549E6033DC.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376045963, "dur":200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376046170, "dur":221, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_2D1A3CE81E2A30A9.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376046392, "dur":488, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376046887, "dur":331, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_7801AD695166142B.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376047219, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376047341, "dur":252, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_D21030A02D3698B8.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376047593, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376047779, "dur":313, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_A78C895E3F9D09B4.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376048093, "dur":287, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376048386, "dur":156, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_A3AEDA067A4E00B3.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376048542, "dur":242, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376048789, "dur":438, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_2E7C67DDAA16C97D.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376049228, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376049354, "dur":188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_742820A688E27790.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376049543, "dur":580, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376050128, "dur":424, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_F459D20976E367C0.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376050552, "dur":687, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376051243, "dur":870, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_2A55E7DBF0791910.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376052114, "dur":1153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376053287, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_8ED8FE3B639303A9.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376053449, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376053574, "dur":370, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_AFEBDA133A58CBE5.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376053945, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376054115, "dur":437, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_98269999F94FEA34.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376054553, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376055173, "dur":184, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_AD31CB19DD860D6F.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376055357, "dur":455, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376055819, "dur":119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_7816C5A0F783DB83.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376055938, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376056115, "dur":264, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_D958D74A33F87E01.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376056380, "dur":1561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376057947, "dur":148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_08AF06BDCB351FBA.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376058095, "dur":413, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376058519, "dur":2509, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_772BB461AE129F34.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376061029, "dur":550, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376061584, "dur":494, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_51985786794073CA.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376062079, "dur":270, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376062384, "dur":341, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_E90F95F41F0DCDBE.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376062726, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376062835, "dur":246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_6B1E3F0FB7F17B0C.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376063082, "dur":856, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376063943, "dur":640, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_CADF340C9EEBB071.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376064584, "dur":569, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376065158, "dur":164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_53FBFC6520D5F26A.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376065323, "dur":505, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376065836, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376066007, "dur":385, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376066420, "dur":213, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376066637, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_CEF96CF5640B5F47.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376066763, "dur":344, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376067121, "dur":314, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376067466, "dur":267, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376067738, "dur":336, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_77BE30E9EA33DCEC.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376068077, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376068210, "dur":450, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376068667, "dur":442, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376069150, "dur":423, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376069578, "dur":398, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376069983, "dur":1512, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376071532, "dur":449, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376071991, "dur":528, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376072525, "dur":853, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376073392, "dur":519, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376073992, "dur":462, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376074466, "dur":773, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376075267, "dur":442, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376075714, "dur":728, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376076470, "dur":372, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376076868, "dur":493, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376077390, "dur":517, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376077928, "dur":479, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376080076, "dur":876, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Stash and Trade\\ShopSystem.cs" }}
,{ "pid":12345, "tid":4, "ts":1755479376081090, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Ship\\SpaceshipVehicle.cs" }}
,{ "pid":12345, "tid":4, "ts":1755479376078413, "dur":3435, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376086006, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxLayerElementVertexColor.cs" }}
,{ "pid":12345, "tid":4, "ts":1755479376081849, "dur":5577, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376089455, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.Mail.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479376094252, "dur":523, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.Pipes.AccessControl.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479376087426, "dur":7643, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376095069, "dur":5868, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376100938, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.Base.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376101007, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376101129, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.ProGrids.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376101203, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376101364, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376101441, "dur":283, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376101755, "dur":488, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1755479376102243, "dur":389, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376102666, "dur":415, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1755479376103082, "dur":1229, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376104327, "dur":410, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376104742, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Csg.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376104826, "dur":321, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376105154, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.BuildTestAssets.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1755479376105228, "dur":254, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376105488, "dur":305, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.BuildTestAssets.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1755479376105799, "dur":1046, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376109276, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-processthreads-l1-1-1.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479376110475, "dur":762, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-namedpipe-l1-1-0.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479376112109, "dur":803, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-handle-l1-1-0.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479376106849, "dur":6743, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376113592, "dur":2808, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376116401, "dur":556, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479376117587, "dur":2638, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479376120655, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479376122430, "dur":551, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479376122981, "dur":1066, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479376124299, "dur":631, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479376116401, "dur":9684, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376126565, "dur":1011, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.State\\StateMachine.cs" }}
,{ "pid":12345, "tid":4, "ts":1755479376126085, "dur":3150, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376129236, "dur":691, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@c19679f07bae\\Runtime\\Core\\HandleOrientation.cs" }}
,{ "pid":12345, "tid":4, "ts":1755479376133106, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@c19679f07bae\\Runtime\\Core\\BuiltinMaterials.cs" }}
,{ "pid":12345, "tid":4, "ts":1755479376129236, "dur":5590, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376134826, "dur":2429, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376137530, "dur":1537, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\GlobalLowResolutionTransparencySettings.cs" }}
,{ "pid":12345, "tid":4, "ts":1755479376139610, "dur":526, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\CullingGroupManager.cs" }}
,{ "pid":12345, "tid":4, "ts":1755479376140506, "dur":644, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\Camera\\HDAdditionalCameraData.cs" }}
,{ "pid":12345, "tid":4, "ts":1755479376141270, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\Accumulation\\DenoisePass.cs" }}
,{ "pid":12345, "tid":4, "ts":1755479376137256, "dur":5666, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376143061, "dur":1592, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Compositor\\ICompositionFilterComponent.cs" }}
,{ "pid":12345, "tid":4, "ts":1755479376142923, "dur":3022, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376145966, "dur":1425, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376147392, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\ScriptGraphAsset.cs" }}
,{ "pid":12345, "tid":4, "ts":1755479376147392, "dur":3780, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376151172, "dur":2228, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376153401, "dur":2797, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376156199, "dur":2591, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376158960, "dur":4400, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376172287, "dur":480, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":4, "ts":1755479376172768, "dur":1749, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner" }}
,{ "pid":12345, "tid":4, "ts":1755479376174517, "dur":292, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger" }}
,{ "pid":12345, "tid":4, "ts":1755479376163360, "dur":11452, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479376174813, "dur":1022327, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479377197141, "dur":1334, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.ProBuilder.Stl.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1755479377198476, "dur":1993, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479377200480, "dur":1335, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.ProBuilder.Poly2Tri.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1755479377201816, "dur":1563, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479377203386, "dur":1374, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1755479377204761, "dur":965, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479377205736, "dur":1002, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479377206777, "dur":6704, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479377213487, "dur":420, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479377213921, "dur":5518, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479377219443, "dur":1101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479377220565, "dur":5013, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":4, "ts":1755479377225583, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479377226268, "dur":6302, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Collections.pdb" }}
,{ "pid":12345, "tid":4, "ts":1755479377232574, "dur":1051, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479377233640, "dur":5948, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/BakeryRuntimeAssembly.pdb" }}
,{ "pid":12345, "tid":4, "ts":1755479377239594, "dur":788, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479377240395, "dur":6453, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb" }}
,{ "pid":12345, "tid":4, "ts":1755479377246853, "dur":498, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479377247363, "dur":8498, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.pdb" }}
,{ "pid":12345, "tid":4, "ts":1755479377255863, "dur":4273945, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376018920, "dur":25474, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376044402, "dur":782, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_50873D227E71BA32.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376045185, "dur":210, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376045404, "dur":321, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_19C4E7C373307C98.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376045725, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376045844, "dur":493, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_90B8FBEE610C9F2E.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376046338, "dur":305, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376046650, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_802A63180084FFE0.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376046814, "dur":211, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376047029, "dur":296, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_BA2A68AB09A36A18.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376047326, "dur":234, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376047561, "dur":73, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_BA2A68AB09A36A18.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376047647, "dur":236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_170E1AD8C176E373.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376047883, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376048014, "dur":315, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_FFD280FCA6DE2694.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376048330, "dur":320, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376048655, "dur":321, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_338473275F88084B.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376048976, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376049098, "dur":336, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_FEF65670303CA9ED.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376049434, "dur":588, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376050027, "dur":486, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_537D35FED0AF04AF.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376050513, "dur":689, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376051209, "dur":798, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_6071707E3B56D340.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376052008, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376052125, "dur":196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_497B5BD213CB63CC.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376052322, "dur":1026, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376053352, "dur":365, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_20652FE087838BAA.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376053717, "dur":193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376053915, "dur":578, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_15D8BD493B8AD51A.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376054494, "dur":675, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376055183, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376055347, "dur":470, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376055823, "dur":457, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_2BBF0F1DD09DEB09.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376056281, "dur":318, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376056604, "dur":152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_D041405E4DB8CB53.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376056757, "dur":755, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376057519, "dur":194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_41E7E83F3C39109E.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376057714, "dur":818, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376058536, "dur":2527, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_2F437DF01E8EE616.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376061064, "dur":529, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376061598, "dur":602, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_80F3A24D117BC0AC.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376062201, "dur":396, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376062638, "dur":255, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8BD69859CFD94B93.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376062893, "dur":956, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376063856, "dur":305, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_FCE263779A4DC55C.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376064162, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376064304, "dur":593, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_5665BA7650FB73B5.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376064898, "dur":323, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376065226, "dur":366, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_027F4219E62E4C69.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376065593, "dur":354, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376065968, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376066087, "dur":280, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376066403, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376066539, "dur":366, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376066911, "dur":353, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376067283, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376067423, "dur":228, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376067661, "dur":302, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376067968, "dur":265, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376068268, "dur":466, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376068743, "dur":410, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376069183, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376069623, "dur":365, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376069994, "dur":1460, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376071463, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376071609, "dur":433, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376072046, "dur":509, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376072560, "dur":814, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376073388, "dur":515, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376073967, "dur":431, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376074472, "dur":734, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376075211, "dur":447, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376075680, "dur":782, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376076487, "dur":382, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376076890, "dur":470, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376077391, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376077909, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376079897, "dur":649, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Inv\\InvItemUtils.cs" }}
,{ "pid":12345, "tid":5, "ts":1755479376078493, "dur":3877, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376086028, "dur":674, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxLayerContainer.cs" }}
,{ "pid":12345, "tid":5, "ts":1755479376082378, "dur":5453, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376087831, "dur":7924, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376095757, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Burst.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376095832, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376095959, "dur":6489, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1755479376102449, "dur":724, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376103174, "dur":71, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1755479376103252, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376103408, "dur":527, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.Base.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1755479376103935, "dur":569, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376104519, "dur":280, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376104815, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Stl.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1755479376104890, "dur":313, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376105208, "dur":317, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Stl.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1755479376105526, "dur":1495, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376107042, "dur":3884, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376110927, "dur":2605, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376113532, "dur":3873, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376117406, "dur":2801, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":5, "ts":1755479376120538, "dur":539, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":5, "ts":1755479376122202, "dur":652, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":5, "ts":1755479376122858, "dur":648, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":5, "ts":1755479376123506, "dur":732, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll" }}
,{ "pid":12345, "tid":5, "ts":1755479376124238, "dur":644, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":5, "ts":1755479376117406, "dur":8453, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376126871, "dur":2915, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\SetUpTearDownCommand.cs" }}
,{ "pid":12345, "tid":5, "ts":1755479376125859, "dur":4822, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376130681, "dur":2129, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376134958, "dur":2517, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Sky\\SkySettings.cs" }}
,{ "pid":12345, "tid":5, "ts":1755479376132810, "dur":5365, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376140504, "dur":644, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Material\\LTCAreaLight\\LTCAreaLight.cs" }}
,{ "pid":12345, "tid":5, "ts":1755479376141148, "dur":631, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Material\\LTCAreaLight\\Generated\\LtcData.BRDF_Ward.cs" }}
,{ "pid":12345, "tid":5, "ts":1755479376141779, "dur":527, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Material\\LTCAreaLight\\Generated\\LtcData.BRDF_OrenNayar.cs" }}
,{ "pid":12345, "tid":5, "ts":1755479376138175, "dur":4377, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376142552, "dur":645, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Debug\\FalseColorDebug.cs" }}
,{ "pid":12345, "tid":5, "ts":1755479376142552, "dur":3239, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376146457, "dur":927, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@bd0e8186c2bc\\Runtime\\GPUDriven\\InstanceData\\InstanceAllocator.cs" }}
,{ "pid":12345, "tid":5, "ts":1755479376145791, "dur":3588, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376150345, "dur":674, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector3\\Vector3MoveTowards.cs" }}
,{ "pid":12345, "tid":5, "ts":1755479376149379, "dur":3082, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376152462, "dur":293, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1755479376152756, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376153445, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376153602, "dur":2498, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376156101, "dur":470, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1755479376156572, "dur":410, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376156991, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376157126, "dur":2464, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479376159591, "dur":1035809, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479377195403, "dur":1672, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.ProBuilder.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1755479377197076, "dur":1112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479377198240, "dur":1433, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.ProBuilder.Csg.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1755479377199674, "dur":1809, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479377201491, "dur":1440, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.TextMeshPro.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1755479377202931, "dur":1460, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479377204411, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479377204566, "dur":7811, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":5, "ts":1755479377212382, "dur":812, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479377213233, "dur":5098, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1755479377218335, "dur":545, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479377218891, "dur":5071, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":5, "ts":1755479377223967, "dur":1024, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479377225006, "dur":6171, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1755479377231182, "dur":763, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479377231960, "dur":6388, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.BuildTestAssets.pdb" }}
,{ "pid":12345, "tid":5, "ts":1755479377238353, "dur":906, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479377239273, "dur":6104, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.pdb" }}
,{ "pid":12345, "tid":5, "ts":1755479377245382, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479377245980, "dur":5530, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Config.Runtime.pdb" }}
,{ "pid":12345, "tid":5, "ts":1755479377251511, "dur":4278322, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376018946, "dur":25456, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376044410, "dur":763, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_72843B12C117041E.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376045174, "dur":227, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376045408, "dur":381, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_D1DCB151E3ACC37A.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376045790, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376045916, "dur":301, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_56216F75A4649408.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376046218, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376046774, "dur":288, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_21912B53026E5E3B.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376047062, "dur":190, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376047259, "dur":250, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_60DE29F3052F9CCA.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376047510, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376047633, "dur":198, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_F7B5610B5E8943DB.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376047832, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376047965, "dur":197, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_2063A01A7F261A99.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376048162, "dur":357, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376048567, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_3687FCD1F31EBD61.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376048730, "dur":284, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376049038, "dur":371, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_6327B91383E9B987.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376049410, "dur":599, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376050015, "dur":457, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_97987AD541C274A1.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376050472, "dur":647, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376051124, "dur":821, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_167C2B98011CE251.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376051946, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376052070, "dur":429, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F3ED478619717848.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376052500, "dur":893, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376053405, "dur":638, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_4DF55899EF65C348.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376054044, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376054173, "dur":360, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_E4872D1F62F03904.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376054534, "dur":1194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376055743, "dur":514, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_B026B1B52A9E9721.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376056258, "dur":376, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376056640, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_CE28B3F5CB254350.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376056817, "dur":1422, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376058244, "dur":2692, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_AB9A4FE286E59CCD.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376060937, "dur":662, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376061605, "dur":532, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_68ABC4657ACC8C1F.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376062138, "dur":286, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376062443, "dur":344, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_634E41B9367B77CF.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376062788, "dur":1123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376063917, "dur":789, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_CB14BD987D5A7AF1.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376064707, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376065205, "dur":317, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_90F065C718290D68.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376065523, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376066022, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376066166, "dur":275, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376066449, "dur":460, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376066916, "dur":334, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376067292, "dur":466, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376067762, "dur":251, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_4A87A716FBA7BB4C.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376068013, "dur":234, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376068257, "dur":696, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376068969, "dur":486, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376071516, "dur":1029, "ph":"X", "name": "WriteResponseFile",  "args": { "detail":"Library/Bee/artifacts/rsp/4747638433968585886.rsp" }}
,{ "pid":12345, "tid":6, "ts":1755479376072548, "dur":841, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376073399, "dur":719, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376074123, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.ProGrids.rsp" }}
,{ "pid":12345, "tid":6, "ts":1755479376074199, "dur":425, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376074631, "dur":585, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376075229, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376075800, "dur":628, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376076454, "dur":380, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376076860, "dur":472, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376077364, "dur":519, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376077907, "dur":504, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376078416, "dur":2738, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376081154, "dur":4637, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376085792, "dur":188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376085981, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376086122, "dur":8757, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1755479376094883, "dur":341, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376095242, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376095381, "dur":546, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Formatters.Json.dll" }}
,{ "pid":12345, "tid":6, "ts":1755479376095381, "dur":5787, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376101169, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Formats.Fbx.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376101241, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376101373, "dur":430, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Formats.Fbx.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1755479376101804, "dur":326, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376102137, "dur":532, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.ProGrids.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1755479376102670, "dur":1021, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376103741, "dur":79, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/BakeryRuntimeAssembly.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376103821, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376103960, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Tayx.Graphy.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376104035, "dur":262, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376104309, "dur":388, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376104704, "dur":313, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/BakeryRuntimeAssembly.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1755479376105017, "dur":755, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376110405, "dur":987, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Grpc.Net.Client.dll" }}
,{ "pid":12345, "tid":6, "ts":1755479376105778, "dur":6454, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376112232, "dur":3444, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376116403, "dur":523, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll" }}
,{ "pid":12345, "tid":6, "ts":1755479376117279, "dur":2966, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":6, "ts":1755479376120568, "dur":641, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":6, "ts":1755479376121209, "dur":953, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":6, "ts":1755479376122162, "dur":1366, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":6, "ts":1755479376123528, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":6, "ts":1755479376124187, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll" }}
,{ "pid":12345, "tid":6, "ts":1755479376115676, "dur":10426, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376126103, "dur":1479, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.State\\IState.cs" }}
,{ "pid":12345, "tid":6, "ts":1755479376127859, "dur":1964, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.State\\Framework\\Graph\\HasStateGraph.cs" }}
,{ "pid":12345, "tid":6, "ts":1755479376129947, "dur":1106, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.State\\FlowState.cs" }}
,{ "pid":12345, "tid":6, "ts":1755479376126103, "dur":5141, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376131249, "dur":3239, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376134916, "dur":1645, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\Utility\\HDUtils.cs" }}
,{ "pid":12345, "tid":6, "ts":1755479376137634, "dur":1284, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\Settings\\FrameSettingsDefaults.cs" }}
,{ "pid":12345, "tid":6, "ts":1755479376139738, "dur":734, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\SceneViewDrawMode.cs" }}
,{ "pid":12345, "tid":6, "ts":1755479376134489, "dur":6664, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376142062, "dur":875, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Lighting\\Shadow\\HDCachedShadowAtlas.cs" }}
,{ "pid":12345, "tid":6, "ts":1755479376143054, "dur":1567, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Lighting\\Shadow\\AdditionalShadowData.cs" }}
,{ "pid":12345, "tid":6, "ts":1755479376141153, "dur":5154, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376148485, "dur":562, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@bd0e8186c2bc\\Runtime\\Volume\\KeyframeUtility.cs" }}
,{ "pid":12345, "tid":6, "ts":1755479376149602, "dur":562, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@bd0e8186c2bc\\Runtime\\Utilities\\TextureGradient.cs" }}
,{ "pid":12345, "tid":6, "ts":1755479376146307, "dur":3857, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376150164, "dur":2598, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376152763, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Samples.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1755479376152838, "dur":518, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376153363, "dur":286, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Samples.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1755479376153650, "dur":459, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376154118, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376154745, "dur":622, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Utilities\\StringUtility.cs" }}
,{ "pid":12345, "tid":6, "ts":1755479376154244, "dur":3589, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376157833, "dur":2926, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479376160759, "dur":1034635, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479377195395, "dur":1679, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.HighDefinition.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1755479377197075, "dur":1171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479377198257, "dur":1372, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1755479377199630, "dur":894, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479377200528, "dur":1499, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Recorder.Base.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1755479377202028, "dur":1331, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479377203366, "dur":1429, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1755479377204796, "dur":712, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479377205516, "dur":1150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479377206684, "dur":6540, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":6, "ts":1755479377213229, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479377213852, "dur":6815, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":6, "ts":1755479377220672, "dur":638, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479377221323, "dur":5567, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":6, "ts":1755479377226895, "dur":1136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479377228130, "dur":7012, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb" }}
,{ "pid":12345, "tid":6, "ts":1755479377235147, "dur":920, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479377236098, "dur":6031, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.KdTree.pdb" }}
,{ "pid":12345, "tid":6, "ts":1755479377242133, "dur":1286, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479377243434, "dur":6634, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Burst.pdb" }}
,{ "pid":12345, "tid":6, "ts":1755479377250070, "dur":4279709, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376018972, "dur":25438, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376044416, "dur":868, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_BCCFE8971C56FC99.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376045285, "dur":379, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376045669, "dur":296, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_FB6662502F548741.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376045966, "dur":493, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376046464, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_8153B6E5C8A1EAEE.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376046610, "dur":420, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376047035, "dur":296, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_361567878F3A0321.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376047331, "dur":253, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376047618, "dur":352, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B4CD312D3ABB23CD.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376047970, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376048128, "dur":248, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_399CFEEAA9224D7C.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376048377, "dur":317, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376048701, "dur":492, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_7D3F3F43795C18C3.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376049193, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376049350, "dur":186, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_188E9A8DFB799D2D.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376049537, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376050175, "dur":396, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_0119BE8FADB7EE8C.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376050572, "dur":687, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376051264, "dur":1086, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_35AEFD4DE2672EBA.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376052351, "dur":1096, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376053451, "dur":434, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_8CD528DD12153FD6.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376053887, "dur":442, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376054337, "dur":221, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_236BAA589C106708.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376054559, "dur":1082, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376055648, "dur":536, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376056189, "dur":215, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_FE6C766828A02245.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376056405, "dur":1000, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376057411, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_230741AFF43D7E0B.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376057529, "dur":967, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376058500, "dur":2563, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_CEFDB7E99B7CF7EA.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376061063, "dur":671, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376061740, "dur":786, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B1D84FA89ACF2C57.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376062526, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376062686, "dur":420, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_4C2ABE42EA45006E.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376063107, "dur":982, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376064094, "dur":849, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_686BE4F657D0DE50.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376064944, "dur":511, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376065463, "dur":191, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_BAE5A32452534A7D.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376065655, "dur":335, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376066002, "dur":273, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376066289, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376066420, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376066498, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376066616, "dur":28321, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1755479376094938, "dur":453, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376095407, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376095521, "dur":58, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_FAE6DBEA21006BE5.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376095603, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376095699, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376095822, "dur":11922, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1755479376107752, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376108199, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376108322, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Collections.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376108388, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376108512, "dur":9821, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Collections.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1755479376118334, "dur":369, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376118719, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376118848, "dur":87, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376118935, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376119077, "dur":32207, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1755479376151290, "dur":490, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376151794, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376151921, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376152006, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376152128, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376152214, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376152341, "dur":351, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1755479376152693, "dur":762, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376153465, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376153619, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376153721, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376153870, "dur":1155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1755479376155026, "dur":349, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376155387, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376155609, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376155688, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376155808, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1755479376155887, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376156004, "dur":314, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1755479376156319, "dur":413, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376156743, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376156868, "dur":2761, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479376159630, "dur":1035754, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479377195386, "dur":1688, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1755479377197075, "dur":1124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479377198242, "dur":1375, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1755479377199618, "dur":859, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479377200484, "dur":1432, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/BakeryRuntimeAssembly.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1755479377201917, "dur":1446, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479377203370, "dur":1699, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1755479377205070, "dur":674, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479377205752, "dur":1071, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479377206846, "dur":7160, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479377214010, "dur":682, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479377214724, "dur":6265, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479377220993, "dur":804, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479377221818, "dur":5249, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479377227071, "dur":958, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479377228073, "dur":6812, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.pdb" }}
,{ "pid":12345, "tid":7, "ts":1755479377234891, "dur":1029, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479377235940, "dur":5736, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.pdb" }}
,{ "pid":12345, "tid":7, "ts":1755479377241682, "dur":1226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479377242923, "dur":6083, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Samples.Runtime.pdb" }}
,{ "pid":12345, "tid":7, "ts":1755479377249007, "dur":1847891, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479379097129, "dur":379097, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379476390, "dur":159, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379476668, "dur":143, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379476930, "dur":155, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379477193, "dur":134, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379477434, "dur":165, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379477705, "dur":442, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379478291, "dur":562, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379478990, "dur":148, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379479253, "dur":80568, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379560027, "dur":187, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379560383, "dur":222, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379560749, "dur":415, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379561296, "dur":175, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379561592, "dur":199, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379561922, "dur":183, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379562241, "dur":171, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379562563, "dur":167, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379562875, "dur":154, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379563146, "dur":316, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379563576, "dur":159372, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379723153, "dur":183, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379723490, "dur":161, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379723858, "dur":154, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379724161, "dur":265, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379724573, "dur":161, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379724865, "dur":269296, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379994318, "dur":176, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379994621, "dur":159, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379994899, "dur":513, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379995527, "dur":273, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379995932, "dur":211, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379996257, "dur":97061, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479380093483, "dur":510, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479380094117, "dur":180, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479380094418, "dur":54920, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":7, "ts":1755479379096900, "dur":1052454, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":7, "ts":1755479380150605, "dur":1378079, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":8, "ts":1755479376048730, "dur":653, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_809AC430EFDBEAE5.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1755479376049384, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376050021, "dur":885, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_9B656C706032BDCD.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1755479376050907, "dur":372, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376051284, "dur":1395, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_45476BA571FB59E8.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1755479376052680, "dur":880, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376053565, "dur":711, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_2D33C28CD6ADE907.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1755479376054276, "dur":1385, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376055668, "dur":1239, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376056915, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A7F1A6B4CF0050A4.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1755479376057057, "dur":1109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376058174, "dur":2668, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_650A7E6E6CF77814.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1755479376060843, "dur":447, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376061299, "dur":647, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_FE60ECE2F1AF16AD.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1755479376061946, "dur":1820, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376063774, "dur":1492, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_229571BCA6026082.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1755479376065266, "dur":519, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376065792, "dur":152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1755479376065945, "dur":473, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376066438, "dur":449, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376066893, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Mathematics.rsp" }}
,{ "pid":12345, "tid":8, "ts":1755479376066946, "dur":319, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376067300, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376067482, "dur":759, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376068251, "dur":451, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376068708, "dur":474, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376069187, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.rsp" }}
,{ "pid":12345, "tid":8, "ts":1755479376069283, "dur":538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376069825, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.RenderPipelines.HighDefinition.Runtime.rsp" }}
,{ "pid":12345, "tid":8, "ts":1755479376069955, "dur":1518, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376071477, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Flow.rsp" }}
,{ "pid":12345, "tid":8, "ts":1755479376071544, "dur":659, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376072247, "dur":1145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376073402, "dur":694, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376074127, "dur":370, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376074502, "dur":759, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376075261, "dur":56, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Domain_Reload.rsp2" }}
,{ "pid":12345, "tid":8, "ts":1755479376075339, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376076028, "dur":859, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376076912, "dur":670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376077587, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/18345886388837496740.rsp" }}
,{ "pid":12345, "tid":8, "ts":1755479376077647, "dur":511, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376078183, "dur":342, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376079722, "dur":816, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Interface\\Cursor\\CustomCursor.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376081693, "dur":27161, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\General\\PlatformButtonController.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376078532, "dur":30322, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376109336, "dur":585, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\UnityTestProtocol\\UtpMessageReporter.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376111036, "dur":882, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\UnityTestProtocol\\TestRunData.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376108854, "dur":3536, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376113838, "dur":1131, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\TestRun\\ITestJobRunner.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376114970, "dur":547, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\TestRun\\ITestJobDataHolder.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376116119, "dur":820, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\TestRun\\Data\\TaskMode.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376116939, "dur":504, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\TestRun\\Data\\TaskInfo.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376117926, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\TestLaunchers\\TestLauncherBase.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376118503, "dur":511, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\TestLaunchers\\RuntimeTestLauncherBase.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376119015, "dur":835, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\TestLaunchers\\RemotePlayerTestController.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376120312, "dur":502, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\TestLaunchers\\PlayerLauncherTestRunSettings.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376112390, "dur":8425, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376120945, "dur":966, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultData.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376122031, "dur":730, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\PlayerConnectionMessageIds.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376122947, "dur":956, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\IRemoteTestResultDataFactory.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376124021, "dur":1422, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsController.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376125443, "dur":2130, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEngine.TestRunner\\TestRunner\\Messages\\IEditModeTestYieldInstruction.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376127999, "dur":820, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRenderer.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376129109, "dur":794, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\PlayerQuitHandler.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376130014, "dur":5305, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEngine.TestRunner\\NUnitExtensions\\TestResultExtensions.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376120815, "dur":14952, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376135915, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\HDRenderPipelineAsset.DefaultResources.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376136544, "dur":564, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\HDRenderPipelineAsset.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376137446, "dur":1553, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\HDRenderPipeline.VolumetricCloud.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376139622, "dur":1499, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\HDRenderPipeline.RenderGraph.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376143468, "dur":590, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\HDDynamicResolution.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376144059, "dur":512, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\HDComputeThickness.cs" }}
,{ "pid":12345, "tid":8, "ts":1755479376135767, "dur":8805, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376144573, "dur":141, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1755479376144715, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376144869, "dur":494, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1755479376145364, "dur":652, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376146028, "dur":416, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376146452, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1755479376146632, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376146825, "dur":467, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1755479376147293, "dur":333, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376147639, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376147816, "dur":119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1755479376147935, "dur":226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376148168, "dur":917, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/StandaloneWindows64_CodeGen/Unity.Collections.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1755479376149086, "dur":391, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376150178, "dur":2880, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\rsp\\4747638433968585886.rsp" }}
,{ "pid":12345, "tid":8, "ts":1755479376149487, "dur":3572, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":8, "ts":1755479376153904, "dur":116, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479376154031, "dur":994547, "ph":"X", "name": "ILPP-Configuration",  "args": { "detail":"Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":8, "ts":1755479377195393, "dur":1682, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Tayx.Graphy.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1755479377197076, "dur":1140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479377198216, "dur":427, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Tayx.Graphy.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1755479377198646, "dur":1345, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Autodesk.Fbx.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1755479377199991, "dur":1667, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479377201667, "dur":1338, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Formats.Fbx.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1755479377203006, "dur":2318, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479377205334, "dur":1103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479377206449, "dur":6699, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":8, "ts":1755479377213157, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479377213721, "dur":7816, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":8, "ts":1755479377221557, "dur":688, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479377222272, "dur":8025, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":8, "ts":1755479377230301, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479377230846, "dur":6645, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":8, "ts":1755479377237496, "dur":670, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479377238179, "dur":6608, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Recorder.pdb" }}
,{ "pid":12345, "tid":8, "ts":1755479377244792, "dur":706, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479377245521, "dur":5563, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.pdb" }}
,{ "pid":12345, "tid":8, "ts":1755479377251085, "dur":4278691, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376048849, "dur":635, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_0EAB73071C382BAA.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376049485, "dur":1176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376050667, "dur":231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_705B406DC1A18CBC.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376050899, "dur":432, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376051335, "dur":1148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_64FEF6FE852B33A5.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376052484, "dur":917, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376053407, "dur":873, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_0D61C2EA468CDF29.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376054281, "dur":1054, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376055385, "dur":1831, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376057222, "dur":1214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_29D4A3872139F6EF.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376058436, "dur":480, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376058922, "dur":2536, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_DB0C012C3D0214BB.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376061458, "dur":207, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376061676, "dur":1628, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_95A72434CABC428C.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376063305, "dur":855, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376064166, "dur":984, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_5BB71BDFC35C44D6.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376065150, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376065708, "dur":308, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376066017, "dur":943, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376066970, "dur":335, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376067312, "dur":376, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376067692, "dur":158, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_51507B821DB9FEDF.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376067851, "dur":183, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376068071, "dur":689, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376068765, "dur":481, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376069250, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":9, "ts":1755479376069329, "dur":438, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376069778, "dur":1804, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376071587, "dur":486, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376072079, "dur":1219, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376073312, "dur":669, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376074009, "dur":479, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376074495, "dur":796, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376075291, "dur":148, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aP.dag/BakeryRuntimeAssembly.rsp2" }}
,{ "pid":12345, "tid":9, "ts":1755479376075449, "dur":689, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376076146, "dur":654, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376076831, "dur":529, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376077388, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376077942, "dur":471, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376079920, "dur":742, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Player\\Boon\\PlayerBoonManager.cs" }}
,{ "pid":12345, "tid":9, "ts":1755479376078419, "dur":4143, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376083185, "dur":537, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxGeometryBase.cs" }}
,{ "pid":12345, "tid":9, "ts":1755479376086219, "dur":527, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxDouble4x4.cs" }}
,{ "pid":12345, "tid":9, "ts":1755479376082562, "dur":6112, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376088675, "dur":510, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1755479376089185, "dur":540, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":9, "ts":1755479376088675, "dur":8178, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376096854, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376096928, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376097077, "dur":66104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1755479376163182, "dur":422, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376163621, "dur":285, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376163916, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376163990, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376164128, "dur":1144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1755479376165273, "dur":344, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376165630, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376165745, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.State.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376165814, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376165929, "dur":329, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.VisualScripting.State.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1755479376166259, "dur":379, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376166650, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376166791, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Assembly-CSharp.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1755479376166941, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376167079, "dur":416, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1755479376168013, "dur":87, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479377384364, "dur":154, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479376170017, "dur":1216914, "ph":"X", "name": "Csc",  "args": { "detail":"Library/Bee/artifacts/1900b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1755479377428561, "dur":307635, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1900b0aP.dag\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":9, "ts":1755479377428436, "dur":309333, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Assembly-CSharp.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1755479377739526, "dur":319, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479377741136, "dur":1003418, "ph":"X", "name": "ILPostProcess",  "args": { "detail":"Library/Bee/artifacts/1900b0aP.dag/post-processed/Assembly-CSharp.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1755479378858524, "dur":235411, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1900b0aP.dag\\post-processed\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":9, "ts":1755479378858362, "dur":235575, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":9, "ts":1755479379093960, "dur":2924, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":9, "ts":1755479379096901, "dur":2432883, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376018996, "dur":25433, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376044449, "dur":630, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_4A744BEFA62889F0.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376045081, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376045209, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_23D96CFCBABAF7C6.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376045345, "dur":356, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376045708, "dur":262, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_E8196077AF0FBF89.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376045971, "dur":190, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376046167, "dur":263, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_FA99FAF2038377D1.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376046431, "dur":497, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376046933, "dur":378, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_2110F87BCCED7F46.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376047312, "dur":248, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376047567, "dur":203, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_A82296CA2FB6B4F1.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376047771, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376047902, "dur":299, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_432C241E42B66359.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376048201, "dur":394, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376048602, "dur":260, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_65A1FFD8AC8F6F4E.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376048863, "dur":193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376049060, "dur":353, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_71744A868049D550.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376049414, "dur":622, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376050041, "dur":441, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_0EDE83D814E30BB2.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376050482, "dur":627, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376051115, "dur":302, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_8104B809D3FF1D86.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376051418, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376051529, "dur":754, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_0A5941573564F27D.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376052284, "dur":1043, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376053332, "dur":305, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_66A11458CF9BFA38.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376053638, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376053766, "dur":660, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_FA74FF884D739BA9.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376054434, "dur":705, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376055165, "dur":239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_998D7F3188D928D3.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376055405, "dur":344, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376055753, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_81391B78C646FD5F.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376055894, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376056015, "dur":302, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_312B396CB1DA6FA3.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376056318, "dur":319, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376056643, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_41EDF9BD9DD25BA0.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376056812, "dur":1131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376057950, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4D170CECB4B7CAB5.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376058080, "dur":477, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376058561, "dur":2482, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_E98A3EC73850C84B.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376061043, "dur":608, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376061657, "dur":444, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_D8584D90221DCFCD.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376062102, "dur":364, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376062471, "dur":440, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_40CC0C6C7DAA3F62.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376062912, "dur":947, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376063865, "dur":293, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_936F2B1A14CC1895.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376064158, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376064308, "dur":592, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_95FD4CA7B0FC4230.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376064901, "dur":404, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376065312, "dur":277, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_511E2F50B9EDAB85.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376065589, "dur":380, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376065974, "dur":200, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8DC0153540044968.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376066175, "dur":624, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376066813, "dur":372, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376067213, "dur":351, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376067577, "dur":368, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376067959, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376068098, "dur":393, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376068497, "dur":477, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376068992, "dur":422, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376069433, "dur":369, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376069815, "dur":1311, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376071159, "dur":383, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376071553, "dur":431, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376071990, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376072487, "dur":844, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376073338, "dur":520, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376073864, "dur":480, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376074388, "dur":805, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376075201, "dur":452, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376075669, "dur":741, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376076443, "dur":382, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376076850, "dur":486, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376077368, "dur":480, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376077884, "dur":480, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376078389, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376079731, "dur":816, "ph":"X", "name": "File",  "args": { "detail":"Assets\\Graphy - Ultimate Stats Monitor\\Runtime\\Shader\\G_GraphShader.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376079056, "dur":3665, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376082722, "dur":527, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxConstraint.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376086003, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.autodesk.fbx@5797ff6b31c7\\Runtime\\Scripts\\FbxAxisSystem.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376082722, "dur":5401, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376088123, "dur":7687, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376095811, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Timeline.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376095938, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376096081, "dur":8991, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1755479376105073, "dur":738, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376105821, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376105950, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376106026, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376106622, "dur":6742, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1755479376113366, "dur":593, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376113973, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376116444, "dur":547, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\Api\\IgnoreTest.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376114091, "dur":3681, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376117772, "dur":2437, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376120210, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1755479376120293, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376120428, "dur":5455, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1755479376125885, "dur":374, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376126273, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376126673, "dur":907, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@c19679f07bae\\Runtime\\Shapes\\Stairs.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376126416, "dur":2091, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376128507, "dur":1352, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@c19679f07bae\\Runtime\\Core\\ProBuilderEnum.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376133037, "dur":568, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@c19679f07bae\\Runtime\\Core\\Log.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376128507, "dur":6336, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376134844, "dur":2627, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\MRTBufferManager.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376137710, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\LineRendering\\Core\\RenderPass\\LineRendering.Pass.Rasterization.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376138314, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\LineRendering\\Core\\RenderPass\\LineRendering.Pass.Geometry.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376139706, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\HDStringConstants.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376141211, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\HDRenderPipelineAsset.Prefiltering.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376134844, "dur":6943, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376142135, "dur":806, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Lighting\\LightDefinition.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376143062, "dur":1565, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Lighting\\Light\\HDShadowRequestUpdateJob.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376145816, "dur":559, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Lighting\\Light\\HDAdditionalLightData.Types.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376141788, "dur":5178, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376146966, "dur":872, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376147838, "dur":3089, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376150928, "dur":2754, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376154720, "dur":603, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Connections\\UnitRelation.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376153682, "dur":2602, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376156738, "dur":852, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticInvokerBase.cs" }}
,{ "pid":12345, "tid":10, "ts":1755479376156284, "dur":4146, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479376160430, "dur":1034963, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479377195394, "dur":1681, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1755479377197076, "dur":1497, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479377198583, "dur":1440, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Recorder.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1755479377200024, "dur":1636, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479377201670, "dur":1549, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Autodesk.Fbx.BuildTestAssets.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1755479377203220, "dur":434, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479377203662, "dur":292, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479377204103, "dur":8030, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":10, "ts":1755479377212139, "dur":812, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479377212963, "dur":7523, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1755479377220491, "dur":506, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479377221010, "dur":4750, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":10, "ts":1755479377225765, "dur":869, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479377226655, "dur":5866, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.pdb" }}
,{ "pid":12345, "tid":10, "ts":1755479377232525, "dur":945, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479377233485, "dur":6134, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Poly2Tri.pdb" }}
,{ "pid":12345, "tid":10, "ts":1755479377239624, "dur":708, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479377240355, "dur":6275, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb" }}
,{ "pid":12345, "tid":10, "ts":1755479377246635, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479377247198, "dur":8489, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.pdb" }}
,{ "pid":12345, "tid":10, "ts":1755479377255693, "dur":4274123, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376019020, "dur":25478, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376044505, "dur":743, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_A7A2D902ABB91631.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376045250, "dur":258, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376045513, "dur":373, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_535F731C2E32ACAF.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376045887, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376046052, "dur":292, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_E425C80947DCA283.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376046345, "dur":449, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376046799, "dur":290, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_52E4CA4914EFB222.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376047090, "dur":195, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376047289, "dur":281, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_AA3EE3779FBDA670.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376047571, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376047755, "dur":314, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_446B83E2EB20F89F.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376048070, "dur":221, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376048298, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_661E32EFAB11E0C4.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376048429, "dur":290, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376048724, "dur":385, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_AC2F3AD54F721085.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376049110, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376049242, "dur":272, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_B987A41AF7E3ACD9.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376049514, "dur":527, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376050046, "dur":489, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_E3346CA010A60E5E.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376050535, "dur":696, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376051236, "dur":945, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1087DD45E442D674.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376052182, "dur":1174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376053369, "dur":473, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_8BAFDBE30AD731E7.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376053843, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376053959, "dur":565, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_F4A92441DB93FA22.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376054525, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376055146, "dur":64, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_F4A92441DB93FA22.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376055225, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376055379, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376055523, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376055724, "dur":492, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_3E1E9ADB918FED5F.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376056216, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376056448, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_0F3F56C0A262E846.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376056584, "dur":927, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376057516, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_7CE8A0C4123B515C.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376057674, "dur":807, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376058487, "dur":2523, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_5E119D02F24AEF89.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376061011, "dur":539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376061570, "dur":515, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_D446D07F07F58287.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376062086, "dur":292, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376062388, "dur":545, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_3A8DBFB10B1AD7F5.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376062934, "dur":985, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376063924, "dur":602, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A2D9ABF83F4B43FF.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376064527, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376065157, "dur":339, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_6309FC21A2C1FF98.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376065497, "dur":449, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376065953, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376066110, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376066711, "dur":368, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376067085, "dur":270, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376067394, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376067572, "dur":425, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376068002, "dur":482, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376068491, "dur":504, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376069003, "dur":452, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376069487, "dur":383, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376069879, "dur":1413, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376071307, "dur":305, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376071617, "dur":448, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376072075, "dur":1073, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376073156, "dur":509, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376073679, "dur":409, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376074130, "dur":386, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376074522, "dur":695, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376075251, "dur":700, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376075956, "dur":628, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376076610, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376076755, "dur":309, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376077089, "dur":547, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376077661, "dur":500, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376078187, "dur":360, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376080026, "dur":533, "ph":"X", "name": "File",  "args": { "detail":"Assets\\_Game\\Scripts\\Experimental\\InteractiveWorldScreenEditor.cs" }}
,{ "pid":12345, "tid":11, "ts":1755479376078554, "dur":3040, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376081595, "dur":5469, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376089453, "dur":524, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Private.CoreLib.dll" }}
,{ "pid":12345, "tid":11, "ts":1755479376087064, "dur":7125, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376094189, "dur":6097, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376100288, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.KdTree.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376100357, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376100479, "dur":2001, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.KdTree.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1755479376102481, "dur":681, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376103196, "dur":262, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376103463, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376103538, "dur":259, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376103802, "dur":451, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Autodesk.Fbx.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1755479376104254, "dur":376, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376104637, "dur":299, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Domain_Reload.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1755479376104936, "dur":1068, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376106009, "dur":79, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1755479376106089, "dur":585, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376106678, "dur":772, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.Recorder.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1755479376107451, "dur":421, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376107883, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376108017, "dur":1957, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376111056, "dur":868, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEditor.TestRunner\\UnityTestProtocol\\IUtpMessageReporter.cs" }}
,{ "pid":12345, "tid":11, "ts":1755479376109974, "dur":3237, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376113212, "dur":2845, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376116284, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":11, "ts":1755479376117353, "dur":2906, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll" }}
,{ "pid":12345, "tid":11, "ts":1755479376120654, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll" }}
,{ "pid":12345, "tid":11, "ts":1755479376122185, "dur":778, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":11, "ts":1755479376122964, "dur":1094, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":11, "ts":1755479376124314, "dur":618, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":11, "ts":1755479376116057, "dur":9901, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376126018, "dur":499, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376127175, "dur":2615, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@c19679f07bae\\Runtime\\Core\\VertexPositioning.cs" }}
,{ "pid":12345, "tid":11, "ts":1755479376131234, "dur":522, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@c19679f07bae\\Runtime\\Core\\TriggerBehaviour.cs" }}
,{ "pid":12345, "tid":11, "ts":1755479376126517, "dur":6910, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376135035, "dur":2422, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\ShaderLibrary\\ShaderVariablesGlobal.cs" }}
,{ "pid":12345, "tid":11, "ts":1755479376133428, "dur":4696, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376140421, "dur":729, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Material\\Water\\WaterDecalAPI.cs" }}
,{ "pid":12345, "tid":11, "ts":1755479376138125, "dur":3774, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376143062, "dur":1568, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Debug\\NVIDIADebugView.cs" }}
,{ "pid":12345, "tid":11, "ts":1755479376141899, "dur":3921, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376146476, "dur":908, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@bd0e8186c2bc\\Runtime\\GPUDriven\\Components\\DisallowGPUDrivenRendering.cs" }}
,{ "pid":12345, "tid":11, "ts":1755479376145820, "dur":2072, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376147892, "dur":2637, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376150530, "dur":2779, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376153309, "dur":2928, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376156237, "dur":2402, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376158639, "dur":127, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376158965, "dur":15851, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479376174816, "dur":1020571, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479377195396, "dur":1679, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1755479377197076, "dur":1137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479377198214, "dur":413, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1755479377198629, "dur":1664, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.ProGrids.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1755479377200294, "dur":2524, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479377202826, "dur":1347, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Mathematics.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1755479377204174, "dur":423, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479377204604, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479377204730, "dur":7341, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":11, "ts":1755479377212079, "dur":834, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479377212941, "dur":5259, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":11, "ts":1755479377218206, "dur":580, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479377218799, "dur":4816, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":11, "ts":1755479377223624, "dur":805, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479377224444, "dur":6355, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1755479377230816, "dur":891, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479377231754, "dur":6620, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":11, "ts":1755479377238379, "dur":939, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479377239330, "dur":6252, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.pdb" }}
,{ "pid":12345, "tid":11, "ts":1755479377245588, "dur":844, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479377246450, "dur":5202, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb" }}
,{ "pid":12345, "tid":11, "ts":1755479377251654, "dur":4278168, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376019047, "dur":25458, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376044506, "dur":795, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_FFA4C3B83280C338.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376045302, "dur":406, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376045713, "dur":446, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_E8FA3C2F22E01EE3.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376046159, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376046381, "dur":191, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_D4C7C89CB2886DF0.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376046572, "dur":431, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376047009, "dur":317, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_E0B24D6CEF174B6F.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376047326, "dur":276, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376047661, "dur":376, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_4E3B38953EFAA34D.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376048037, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376048174, "dur":215, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_68AF68153BC08EC6.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376048389, "dur":352, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376048746, "dur":552, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_EEC530FD6B30B53C.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376049298, "dur":674, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376049978, "dur":237, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_481BE12B510E2DA0.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376050216, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376050376, "dur":323, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_67EDAB9D5F05D46D.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376050700, "dur":560, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376051267, "dur":793, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_05FCF2537FA3C4D4.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376052061, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376052210, "dur":403, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_CA44722F1B054DFB.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376052614, "dur":777, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376053400, "dur":933, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_2D370078DA452AA6.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376054333, "dur":812, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376055180, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376055369, "dur":381, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/StandaloneWindows64_CodeGen/UnityEngine.TestRunner.rsp" }}
,{ "pid":12345, "tid":12, "ts":1755479376055751, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376056418, "dur":164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_426310809050BC36.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376056583, "dur":924, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376057512, "dur":253, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AB4CD292DE0C13CA.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376057766, "dur":809, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376058581, "dur":2455, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_BF153581DA8E0E80.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376061037, "dur":689, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376061731, "dur":579, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_9671EF8343245704.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376062311, "dur":299, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376062646, "dur":302, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_50AB9A065608F145.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376062948, "dur":1094, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376064050, "dur":871, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_4862FAC2C64D7F3B.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376064922, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376065591, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_699976B887BBF5EA.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376065734, "dur":296, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376066084, "dur":287, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376066422, "dur":360, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376066843, "dur":400, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376067282, "dur":437, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376067722, "dur":271, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_33352EA77CF2B9C9.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376067994, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376068154, "dur":413, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376068578, "dur":464, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376069071, "dur":460, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376069539, "dur":431, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376069985, "dur":1499, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376071524, "dur":411, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376071942, "dur":398, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376072346, "dur":972, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376073332, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376073893, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376074471, "dur":745, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376075253, "dur":458, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376075717, "dur":790, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376076535, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376076685, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376076832, "dur":511, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376077372, "dur":507, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376077903, "dur":494, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376078401, "dur":2692, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376081093, "dur":2526, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376083619, "dur":2846, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376089258, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":12, "ts":1755479376091874, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":12, "ts":1755479376086465, "dur":6862, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376093798, "dur":570, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.VisualBasic.dll" }}
,{ "pid":12345, "tid":12, "ts":1755479376099811, "dur":596, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Localization.dll" }}
,{ "pid":12345, "tid":12, "ts":1755479376093328, "dur":7079, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376100408, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Poly2Tri.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1755479376100482, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376100609, "dur":3966, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aP.dag/Unity.ProBuilder.Poly2Tri.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1755479376104576, "dur":578, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376105164, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376106813, "dur":506, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.Extensions.dll" }}
,{ "pid":12345, "tid":12, "ts":1755479376107319, "dur":538, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.dll" }}
,{ "pid":12345, "tid":12, "ts":1755479376108181, "dur":500, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Cryptography.KeyDerivation.dll" }}
,{ "pid":12345, "tid":12, "ts":1755479376110428, "dur":797, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Server.dll" }}
,{ "pid":12345, "tid":12, "ts":1755479376105281, "dur":5944, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376111226, "dur":3249, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376116439, "dur":514, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.2.0f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":12, "ts":1755479376114476, "dur":3222, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376118327, "dur":1888, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEngine.TestRunner\\Utils\\Utils.cs" }}
,{ "pid":12345, "tid":12, "ts":1755479376121009, "dur":926, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\IMonoBehaviourTest.cs" }}
,{ "pid":12345, "tid":12, "ts":1755479376117699, "dur":5136, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376124878, "dur":1781, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@a6f5be5f149c\\UnityEngine.TestRunner\\NUnitExtensions\\IAsyncTestAssemblyBuilder.cs" }}
,{ "pid":12345, "tid":12, "ts":1755479376122836, "dur":4302, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376127280, "dur":2616, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.probuilder@c19679f07bae\\Runtime\\Core\\SharedVertex.cs" }}
,{ "pid":12345, "tid":12, "ts":1755479376127139, "dur":7627, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376136069, "dur":1564, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\RenderPipeline\\RenderPass\\CustomPass\\CustomPass.cs" }}
,{ "pid":12345, "tid":12, "ts":1755479376134766, "dur":4182, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376138949, "dur":2628, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376144410, "dur":1016, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.high-definition@28765669b6fe\\Runtime\\Lighting\\Reflection\\HDAdditionalReflectionData.cs" }}
,{ "pid":12345, "tid":12, "ts":1755479376141577, "dur":3850, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376145473, "dur":2332, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376147809, "dur":3163, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376150972, "dur":2554, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376154199, "dur":1107, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Dictionaries\\AddDictionaryItem.cs" }}
,{ "pid":12345, "tid":12, "ts":1755479376153526, "dur":3694, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376157220, "dur":2554, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479376159774, "dur":1035617, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479377195398, "dur":1678, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1755479377197077, "dur":1192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479377198274, "dur":1590, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Unity.Timeline.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1755479377199864, "dur":1696, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479377201567, "dur":1256, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aP.dag/post-processed/Domain_Reload.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1755479377202824, "dur":753, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479377203585, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479377204265, "dur":7742, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":12, "ts":1755479377212018, "dur":1010, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479377213044, "dur":6299, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Recorder.dll" }}
,{ "pid":12345, "tid":12, "ts":1755479377219349, "dur":1133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479377220499, "dur":5431, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":12, "ts":1755479377225935, "dur":807, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479377226755, "dur":6117, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualEffectGraph.Runtime.pdb" }}
,{ "pid":12345, "tid":12, "ts":1755479377232877, "dur":967, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479377233858, "dur":6308, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Recorder.Base.pdb" }}
,{ "pid":12345, "tid":12, "ts":1755479377240170, "dur":880, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479377241066, "dur":6297, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.ProBuilder.Stl.pdb" }}
,{ "pid":12345, "tid":12, "ts":1755479377247364, "dur":1611001, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479378858529, "dur":432, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1900b0aP.dag\\post-processed\\Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":12, "ts":1755479378858366, "dur":597, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":12, "ts":1755479378858985, "dur":2421, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":12, "ts":1755479378861411, "dur":2668379, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479381541384, "dur":4223, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 34500, "tid": 16971, "ts": 1755479381548486, "dur": 4057, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 34500, "tid": 16971, "ts": 1755479381552678, "dur": 4314, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 34500, "tid": 16971, "ts": 1755479381545768, "dur": 11262, "ph": "X", "name": "Write chrome-trace events", "args": {} },

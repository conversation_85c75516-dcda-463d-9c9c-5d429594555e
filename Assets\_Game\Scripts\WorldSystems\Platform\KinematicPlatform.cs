using UnityEngine;
using KinematicCharacterController;
using System;
using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class KinematicPlatform : MonoBehaviour, IMoverController
{
    [System.Serializable]
    public class PlatformWaypoint
    {
        public Vector3 position;
        public float waitTime = 1f;
        public GameObject referenceObject;

        public void UpdateFromReference()
        {
            if (referenceObject != null)
            {
                position = referenceObject.transform.position;
            }
        }
    }

    /// <summary>
    /// Apply a previously-saved runtime state for persistence.
    /// </summary>
    // Add this method to your KinematicPlatform.cs to properly restore state
// Replace the existing ApplyPersistenceState method with this fixed version:

public void ApplyPersistenceState(
    int waypointIndex,
    int directionMultiplier,
    bool isMoving,
    bool isWaiting,
    float waitTimeRemaining,
    float currentTime,
    float journeyDuration,
    Vector3 startPosition,
    Vector3 targetPosition,
    MotionCurveType savedMotionType,
    float savedCurvePower,
    float savedMoveSpeed,
    bool savedIsLooping)
{
    // Apply config
    motionType = savedMotionType;
    curvePower = savedCurvePower;
    moveSpeed = savedMoveSpeed;
    isLooping = savedIsLooping;

    // Validate and set waypoint index
    if (waypoints.Count > 0)
    {
        waypointIndex = Mathf.Clamp(waypointIndex, 0, waypoints.Count - 1);
    }
    else
    {
        waypointIndex = 0;
        isMoving = false; // Can't move without waypoints
    }

    _currentWaypointIndex = waypointIndex;
    _directionMultiplier = Mathf.Clamp(directionMultiplier, -1, 1);
    if (_directionMultiplier == 0) _directionMultiplier = 1;

    // Derive a safe movement state in case the object was disabled during save and flags were cleared
    bool hasValidSegment = journeyDuration > 0f && (startPosition - targetPosition).sqrMagnitude > 1e-6f;
    bool wasMidSegment = hasValidSegment && currentTime > 0f && currentTime < journeyDuration;
    bool shouldContinueMoving = isMoving || wasMidSegment;
    bool isActuallyWaiting = isWaiting && !wasMidSegment;

    // Handle different states
    if (isActuallyWaiting && shouldContinueMoving)
    {
        // Platform was waiting at a waypoint
        _isWaiting = true;
        _isMoving = true;
        _waitTimeCounter = waitTimeRemaining;
        
        // Snap to the waypoint position
        if (waypoints.Count > 0)
        {
            Vector3 waypointPos = waypoints[_currentWaypointIndex].position;
            transform.position = waypointPos;
            if (_mover != null)
            {
                _mover.SetPositionAndRotation(waypointPos, transform.rotation);
            }
            else if (_rigidbody != null)
            {
                _rigidbody.position = waypointPos;
            }
        }
        
        // Reset journey variables since we're waiting
        _currentTime = 0f;
        _journeyProgress = 0f;
        _journeyDuration = 0f;
        
        Debug.Log($"[Platform] Restored waiting state at waypoint {_currentWaypointIndex}, wait time remaining: {waitTimeRemaining}");
    }
    else if (shouldContinueMoving && hasValidSegment)
    {
        // Platform was moving between waypoints
        _isMoving = true;
        _isWaiting = false;
        
        // Restore journey parameters
        _startPosition = startPosition;
        _targetPosition = targetPosition;
        _journeyLength = Vector3.Distance(_startPosition, _targetPosition);
        _journeyDuration = Mathf.Max(0.0001f, journeyDuration);
        
        // IMPORTANT: Properly restore time and progress
        _currentTime = Mathf.Clamp(currentTime, 0f, _journeyDuration);
        _journeyProgress = Mathf.Clamp01(_currentTime / _journeyDuration);
        
        // Calculate and apply the current position based on progress
        float curvedT = ApplyMotionCurve(_journeyProgress);
        Vector3 restoredPosition = Vector3.Lerp(_startPosition, _targetPosition, curvedT);
        
        // Apply the position
        transform.position = restoredPosition;
        if (_mover != null)
        {
            _mover.SetPositionAndRotation(restoredPosition, transform.rotation);
        }
        else if (_rigidbody != null)
        {
            _rigidbody.position = restoredPosition;
            _rigidbody.rotation = transform.rotation;
        }
        
        Debug.Log($"[Platform] Restored moving state: waypoint {_currentWaypointIndex}, progress {_journeyProgress:F2} ({_currentTime:F1}s / {_journeyDuration:F1}s)");
        Debug.Log($"[Platform] Position: from {_startPosition} to {_targetPosition}, current: {restoredPosition}");
    }
    else
    {
        // Platform was stopped or in an invalid state
        _isMoving = false;
        _isWaiting = false;
        _waitTimeCounter = 0f;
        _currentTime = 0f;
        _journeyProgress = 0f;
        _journeyDuration = 0f;
        
        // Snap to current waypoint if valid
        if (waypoints.Count > 0 && _currentWaypointIndex >= 0 && _currentWaypointIndex < waypoints.Count)
        {
            Vector3 waypointPos = waypoints[_currentWaypointIndex].position;
            transform.position = waypointPos;
            if (_mover != null)
            {
                _mover.SetPositionAndRotation(waypointPos, transform.rotation);
            }
            else if (_rigidbody != null)
            {
                _rigidbody.position = waypointPos;
            }
        }
        
        Debug.Log($"[Platform] Restored stopped state at waypoint {_currentWaypointIndex}");
    }
    
    // Ensure physics mover knows about the new position
    if (_mover != null)
    {
        _mover.SetPosition(transform.position);
        _mover.SetRotation(transform.rotation);
    }
}



    public enum MotionCurveType
    {
        Linear,
        EaseInOut,
        EaseIn,
        EaseOut
    }

    [Serializable]
    public class PlatformShakeSettings
    {
        public bool enable = true;
        [Tooltip("Base oscillation frequency for camera shake")] public float frequency = 2f;
        [Tooltip("Local position shake amplitude")] public Vector3 positionAmplitude = new Vector3(0f, 0.03f, 0f);
        [Tooltip("Local rotation shake amplitude (degrees)")] public Vector3 rotationAmplitude = new Vector3(0.5f, 0.5f, 0.5f);
        [Tooltip("Use Perlin noise instead of sine waves")] public bool usePerlinNoise = true;
        [Tooltip("Baseline intensity applied at all times")] public float constantIntensity = 1f;
        [Tooltip("Extra intensity scaled by platform movement speed")] public float movementIntensityMultiplier = 1f;
        [Tooltip("Intensity multiplier while the platform is waiting at a waypoint")] public float intensityWhileWaiting = 0.5f;
        [Tooltip("Speed at which movement-based intensity reaches maximum")] public float speedForMaxIntensity = 5f;
    }

    [Header("Platform Settings")]
    [SerializeField] private List<PlatformWaypoint> waypoints = new List<PlatformWaypoint>();
    [SerializeField] private bool isLooping = true;
    [SerializeField] private float moveSpeed = 5f;
    [SerializeField] private float positionTolerance = 0.01f;

    [Header("Motion Settings")]
    [SerializeField] private MotionCurveType motionType = MotionCurveType.EaseInOut;

    [Tooltip("Higher values make the easing curves more dramatic")]
    [Range(1f, 5f)]
    [SerializeField] private float curvePower = 2f;

    [Header("Passenger Settings")]
    [Tooltip("If true, any KinematicCharacterMotor inside a Passenger Zone attached to this platform will be force-attached to this platform's rigidbody to inherit its velocity (helps at high speeds)")]
    [SerializeField] private bool attachMotorsInsideZone = true;
    [Tooltip("Apply per-fixed-update velocity/rotation influence to dynamic rigidbodies riding the platform (usually not needed if parenting is used)")]
    [SerializeField] private bool applyRigidbodyInfluence = false;

    // Expose a read-only toggle so other systems (e.g., passenger zones) can fully disable any rigidbody handling
    public bool RigidbodyInfluenceEnabled => applyRigidbodyInfluence;

    [Header("Debug Settings")]
    [SerializeField] private bool showDebugControls = true;
    [SerializeField] private bool showDebugInfo = false;
    [SerializeField] private PlayerDebugDisplay debugDisplay;

    [Header("Camera Shake")]
    [SerializeField] private PlatformShakeSettings cameraShake = new PlatformShakeSettings();

    private PhysicsMover _mover;
    private Rigidbody _rigidbody;
    private readonly HashSet<KinematicCharacterController.KinematicCharacterMotor> _passengerMotors = new HashSet<KinematicCharacterController.KinematicCharacterMotor>();
    private readonly HashSet<Rigidbody> _passengerRigidbodies = new HashSet<Rigidbody>();
    private readonly Dictionary<Rigidbody, Vector3> lastPlatformInfluence = new Dictionary<Rigidbody, Vector3>();
    private int _currentWaypointIndex = 0;
    private int _directionMultiplier = 1;
    private float _waitTimeCounter = 0f;
    private bool _isWaiting = false;
    private bool _isMoving = false;
    private bool _initialized = false;
    private string _debugStatus = "Idle";
    private float _debugTimer = 0f;
    private float _debugCurveValue = 0f;
    private bool _sleepMode = false;
    private bool _holdForPlayerRestore = false;
    [SerializeField] private string platformId = "";

    // Journey tracking variables
    private Vector3 _startPosition;
    private Vector3 _targetPosition;
    private float _journeyLength;
    private float _journeyProgress = 0f;
    private float _currentTime = 0f;
    private float _journeyDuration = 0f;
    
    // Persistence/events API
    public event Action MovementStarted;
    public event Action MovementStopped;
    public event Action<float> WaitStarted; // duration
    public event Action WaitEnded;
    public event Action<int> WaypointArrived; // index
    public event Action<int, Vector3, Vector3, float> SegmentInitialized; // (waypoint, start, target, duration)
    public event Action<int> DirectionChanged; // new direction multiplier
    
    // Public accessors for persistence state
    public PlatformShakeSettings CameraShake => cameraShake;
    public int CurrentWaypointIndex => _currentWaypointIndex;
    public int DirectionMultiplier => _directionMultiplier;
    public bool IsWaiting => _isWaiting;
    public bool IsMoving => _isMoving;
    public float WaitTimeRemaining => _waitTimeCounter;
    public float CurrentTime => _currentTime;
    public float JourneyDuration => _journeyDuration;
    public Vector3 StartPosition => _startPosition;
    public Vector3 TargetPosition => _targetPosition;
    public float PositionTolerance => positionTolerance;
    public MotionCurveType MotionType => motionType;
    public float CurvePower => curvePower;
    public float MoveSpeed => moveSpeed;
    public bool IsLooping => isLooping;
    public int WaypointCount => waypoints != null ? waypoints.Count : 0;
    public Vector3 CurrentVelocity => _mover != null ? _mover.Velocity : Vector3.zero;

    public Vector3 GetWaypointPosition(int index)
    {
        if (waypoints == null || waypoints.Count == 0) return transform.position;
        index = Mathf.Clamp(index, 0, waypoints.Count - 1);
        return waypoints[index].position;
    }

    private void Awake()
    {
        InitializePlatform();

        // Generate a robust ID for persistence if not already set
        if (string.IsNullOrEmpty(platformId))
        {
            platformId = GenerateDefaultPlatformId();
        }
    }

    private void Update()
    {
        if (showDebugControls)
        {
            if (Input.GetKeyDown(KeyCode.Keypad1)) StartMoving();
            if (Input.GetKeyDown(KeyCode.Keypad2)) StopMoving();
            if (Input.GetKeyDown(KeyCode.Keypad3)) ReverseDirection();
        }

        UpdateDebugInfo();
    }

    private void UpdateDebugInfo()
    {
        if (!showDebugInfo && debugDisplay == null) return;

        float distance = 0f;
        if (_isWaiting)
        {
            _debugTimer = _waitTimeCounter;
            _debugStatus = $"Waiting: {_debugTimer:F1}s";
        }
        else if (_isMoving)
        {
            Vector3 currentPos = transform.position;
            Vector3 targetPos = waypoints[_currentWaypointIndex].position;
            distance = Vector3.Distance(currentPos, targetPos);
            _debugStatus = $"Moving to point {_currentWaypointIndex}";
        }
        else
        {
            _debugStatus = "Stopped";
        }

        // Send debug info to player debug display if available
        if (debugDisplay != null && (_isMoving || _isWaiting))
        {
            debugDisplay.UpdatePlatformDebug(
                _debugStatus,
                _currentWaypointIndex,
                waypoints.Count - 1,
                _directionMultiplier,
                motionType.ToString(),
                curvePower,
                _journeyProgress,
                _debugCurveValue,
                distance  // Pass the distance value here
            );
        }
    }



    private void OnValidate()
    {
        UpdateWaypointsFromReferences();

        // Try to find debug display in scene if not assigned
        if (debugDisplay == null)
        {
            debugDisplay = FindFirstObjectByType<PlayerDebugDisplay>();
        }
    }

    private void InitializePlatform()
    {
        if (_initialized) return;

        _rigidbody = gameObject.GetComponent<Rigidbody>();
        if (_rigidbody == null)
            _rigidbody = gameObject.AddComponent<Rigidbody>();

        _mover = gameObject.GetComponent<PhysicsMover>();
        if (_mover == null)
            _mover = gameObject.AddComponent<PhysicsMover>();

        _rigidbody.isKinematic = true;
        _rigidbody.interpolation = RigidbodyInterpolation.None;
        _rigidbody.collisionDetectionMode = CollisionDetectionMode.ContinuousSpeculative;
        _mover.MoveWithPhysics = true;
        _mover.MoverController = this;

        _isMoving = false;
        _initialized = true;
        _debugStatus = "Initialized";
    }

    private void OnDisable()
    {
        // Do not change moving state here to avoid corrupting persistence save on shutdown
        // Ensure any attached motors are detached cleanly
        if (_passengerMotors.Count > 0)
        {
            foreach (var motor in _passengerMotors)
            {
                if (motor != null && motor.AttachedRigidbodyOverride == _rigidbody)
                {
                    motor.AttachedRigidbodyOverride = null;
                }
            }
        }
        _passengerMotors.Clear();
        _passengerRigidbodies.Clear();
        lastPlatformInfluence.Clear();

        // Unregister from manager handled by manager discovery
    }

    private void OnEnable()
    {
        // Registration handled by manager discovery
    }

    // Passenger API used by Passenger Zone component
    public void RegisterPassenger(KinematicCharacterController.KinematicCharacterMotor motor)
    {
        if (motor == null) return;
        _passengerMotors.Add(motor);
        if (attachMotorsInsideZone)
        {
            motor.AttachedRigidbodyOverride = _rigidbody;
        }
    }

    public void UnregisterPassenger(KinematicCharacterController.KinematicCharacterMotor motor)
    {
        if (motor == null) return;
        _passengerMotors.Remove(motor);
        if (attachMotorsInsideZone && motor.AttachedRigidbodyOverride == _rigidbody)
        {
            motor.AttachedRigidbodyOverride = null;
        }
    }

    public void RegisterPassengerRigidbody(Rigidbody rb)
    {
        if (rb == null) return;
        if (rb == _rigidbody) return;
        _passengerRigidbodies.Add(rb);
    }

    public void UnregisterPassengerRigidbody(Rigidbody rb)
    {
        if (rb == null) return;
        _passengerRigidbodies.Remove(rb);
        lastPlatformInfluence.Remove(rb);
    }

    private void FixedUpdate()
    {
        if (IsMovementBlocked(out _)) return;
        if (!applyRigidbodyInfluence) return; // Skip influence system entirely unless explicitly enabled
        if (_passengerRigidbodies.Count == 0) return;

        float dt = Time.fixedDeltaTime;
        Vector3 platformVelocity = _mover != null ? _mover.Velocity : Vector3.zero;
        Vector3 angularVel = _mover != null ? _mover.AngularVelocity : Vector3.zero; // radians/sec

        foreach (var rb in _passengerRigidbodies)
        {
            if (rb == null || rb.isKinematic) continue;

            // Calculate rotational contribution relative to platform pivot
            Vector3 pivot = transform.position;
            Vector3 relativePos = rb.position - pivot;
            Vector3 rotationalVelocity = Vector3.Cross(angularVel, relativePos);
            Vector3 targetVelocity = platformVelocity + rotationalVelocity;

            // Blend platform velocity with passenger's existing velocity influence
            float blendFactor = 0.8f;
            lastPlatformInfluence.TryGetValue(rb, out Vector3 lastInf);
            Vector3 passengerOwnVelocity = rb.linearVelocity - lastInf;
            rb.linearVelocity = targetVelocity + (passengerOwnVelocity * blendFactor);

            // Store for next frame
            lastPlatformInfluence[rb] = targetVelocity;

            // Apply angular rotation smoothly
            if (angularVel != Vector3.zero)
            {
                Quaternion deltaRot = Quaternion.Euler(Mathf.Rad2Deg * angularVel * dt);
                rb.MoveRotation(deltaRot * rb.rotation);
            }

            rb.WakeUp();
        }
    }

    public void UpdateMovement(out Vector3 goalPosition, out Quaternion goalRotation, float deltaTime)
    {
        // Start with current position/rotation
        goalPosition = transform.position;
        goalRotation = transform.rotation;

        if (IsMovementBlocked(out string reason))
        {
            _debugStatus = reason;
            return;
        }

        // Early exit checks
        if (!_isMoving || waypoints.Count < 2)
        {
            _debugStatus = "Not moving - Waiting for start command";
            return;
        }

        if (_isWaiting)
        {
            UpdateWaitTime(deltaTime);
            return;
        }

        // Validate current waypoint index
        if (_currentWaypointIndex < 0 || _currentWaypointIndex >= waypoints.Count)
        {
            Debug.LogError($"Invalid waypoint index: {_currentWaypointIndex}");
            _isMoving = false;
            return;
        }

        // Update current journey status
        _targetPosition = waypoints[_currentWaypointIndex].position;
        float remainingDistance = Vector3.Distance(goalPosition, _targetPosition);

        // Check if we arrived at the waypoint
        if (remainingDistance <= positionTolerance)
        {
            // We've reached the current waypoint
            StartWaiting();
            return;
        }

        // Update time-based journey progress
        _currentTime += deltaTime;
        _journeyProgress = _currentTime / _journeyDuration;

        // Apply motion curve to the progress
        float curvedProgress = ApplyMotionCurve(_journeyProgress);
        _debugCurveValue = curvedProgress;

        // Calculate position using the curved progress
        if (useSplineMovement && _waypointCountCached >= 2)
        {
            // Catmull-Rom across the current segment using neighboring waypoints
            int p1Index = _previousWaypointIndex;
            int p2Index = _currentWaypointIndex;
            int p0Index = Mathf.Clamp(p1Index - _directionMultiplier, 0, _waypointCountCached - 1);
            int p3Index = Mathf.Clamp(p2Index + _directionMultiplier, 0, _waypointCountCached - 1);

            Vector3 p0 = waypoints[p0Index].position;
            Vector3 p1 = waypoints[p1Index].position;
            Vector3 p2 = waypoints[p2Index].position;
            Vector3 p3 = waypoints[p3Index].position;

            goalPosition = CatmullRom(p0, p1, p2, p3, curvedProgress);
        }
        else
        {
            goalPosition = Vector3.Lerp(_startPosition, _targetPosition, curvedProgress);
        }
    }

    private bool IsMovementBlocked(out string reason)
    {
        if (_sleepMode)
        {
            reason = "Sleeping";
            return true;
        }
        if (_holdForPlayerRestore)
        {
            reason = "Held for player restore";
            return true;
        }
        if (PersistenceManager.IsRestoring)
        {
            reason = "Restoring (movement gated)";
            return true;
        }
        reason = null;
        return false;
    }

    private void UpdateWaitTime(float deltaTime)
    {
        if (_waitTimeCounter > 0)
        {
            _waitTimeCounter -= deltaTime;
        }
        else
        {
            _isWaiting = false;
            _debugStatus = "Wait complete, resuming movement";
            WaitEnded?.Invoke();

            // IMPORTANT: This is where we determine the next waypoint
            int nextWaypoint = _currentWaypointIndex + _directionMultiplier;

            // Check if we need to reverse direction or stop
            if (nextWaypoint < 0 || nextWaypoint >= waypoints.Count)
            {
                if (isLooping)
                {
                    // Reverse direction at boundaries
                    _directionMultiplier *= -1;

                    // Recalculate next waypoint with new direction
                    nextWaypoint = _currentWaypointIndex + _directionMultiplier;

                    Debug.Log($"Reversing direction at waypoint {_currentWaypointIndex}. New direction: {_directionMultiplier}");
                    DirectionChanged?.Invoke(_directionMultiplier);
                }
                else
                {
                    // Stop moving if not looping
                    _isMoving = false;
                    _debugStatus = "Reached end of path";
                    MovementStopped?.Invoke();
                    return;
                }
            }

            // Update current waypoint index
            _currentWaypointIndex = nextWaypoint;

            // Initialize the next journey segment
            InitializeJourneyToNextWaypoint();
        }
    }

    private float ApplyMotionCurve(float progress)
    {
        // Clamp progress to 0-1 range
        progress = Mathf.Clamp01(progress);

        // Apply different curve calculations based on the selected motion type
        switch (motionType)
        {
            case MotionCurveType.Linear:
                return progress;

            case MotionCurveType.EaseInOut:
                // Sigmoid-like curve: slower at start and end, faster in middle
                if (progress < 0.5f)
                    return Mathf.Pow(2f * progress, curvePower) / 2f;
                else
                    return 1f - Mathf.Pow(2f * (1f - progress), curvePower) / 2f;

            case MotionCurveType.EaseIn:
                // Power curve: slow at start, accelerates towards end
                return Mathf.Pow(progress, curvePower);

            case MotionCurveType.EaseOut:
                // Inverse power: fast at start, decelerates towards end
                return 1f - Mathf.Pow(1f - progress, curvePower);

            default:
                return progress;
        }
    }

    private float InverseMotionCurve(float curved)
    {
        curved = Mathf.Clamp01(curved);
        switch (motionType)
        {
            case MotionCurveType.Linear:
                return curved;
            case MotionCurveType.EaseIn:
                return Mathf.Pow(curved, 1f / Mathf.Max(0.0001f, curvePower));
            case MotionCurveType.EaseOut:
                return 1f - Mathf.Pow(1f - curved, 1f / Mathf.Max(0.0001f, curvePower));
            case MotionCurveType.EaseInOut:
                if (curved < 0.5f)
                {
                    // curved = (2x)^p / 2  =>  x = 0.5 * (2*curved)^(1/p)
                    return 0.5f * Mathf.Pow(2f * curved, 1f / Mathf.Max(0.0001f, curvePower));
                }
                else
                {
                    // curved = 1 - (2(1-x))^p / 2  =>  x = 1 - 0.5 * (2*(1-curved))^(1/p)
                    return 1f - 0.5f * Mathf.Pow(2f * (1f - curved), 1f / Mathf.Max(0.0001f, curvePower));
                }
            default:
                return curved;
        }
    }

    public void ApplyDerivedStateFromWorldPosition(Vector3 savedWorldPosition)
    {
        if (waypoints == null || waypoints.Count < 2) return;

        // Determine the segment from previous waypoint to current target based on direction
        int prevIndex = Mathf.Clamp(_currentWaypointIndex - _directionMultiplier, 0, waypoints.Count - 1);
        Vector3 startGuess = waypoints[prevIndex].position;
        Vector3 targetGuess = waypoints[_currentWaypointIndex].position;

        Vector3 seg = targetGuess - startGuess;
        float segSqr = seg.sqrMagnitude;
        if (segSqr < 1e-6f || moveSpeed <= 0.0001f)
        {
            // Degenerate; just snap to saved position and stop
            transform.position = savedWorldPosition;
            if (_mover != null)
            {
                _mover.SetPositionAndRotation(savedWorldPosition, transform.rotation);
            }
            else if (_rigidbody != null)
            {
                _rigidbody.position = savedWorldPosition;
            }
            _isMoving = false;
            _isWaiting = false;
            _journeyDuration = 0f;
            _currentTime = 0f;
            _journeyProgress = 0f;
            return;
        }

        // Project saved world position onto the segment to get the curved T used by movement
        float tLinear = Mathf.Clamp01(Vector3.Dot(savedWorldPosition - startGuess, seg) / segSqr);
        // Invert the motion curve to get the raw progress
        float progress = Mathf.Clamp01(InverseMotionCurve(tLinear));

        _startPosition = startGuess;
        _targetPosition = targetGuess;
        _journeyLength = Mathf.Sqrt(segSqr);
        _journeyDuration = _journeyLength / Mathf.Max(0.0001f, moveSpeed);
        _currentTime = progress * _journeyDuration;
        _journeyProgress = progress;
        _isMoving = true;
        _isWaiting = false;

        // Apply the position so physics mover is in sync
        Vector3 restored = Vector3.Lerp(_startPosition, _targetPosition, tLinear);
        transform.position = restored;
        if (_mover != null)
        {
            _mover.SetPositionAndRotation(restored, transform.rotation);
            _mover.SetPosition(restored);
            _mover.SetRotation(transform.rotation);
        }
        else if (_rigidbody != null)
        {
            _rigidbody.position = restored;
            _rigidbody.rotation = transform.rotation;
        }
    }

    private void StartWaiting()
    {
        _isWaiting = true;
        _waitTimeCounter = waypoints[_currentWaypointIndex].waitTime;
        _debugStatus = "Starting wait period";
        WaypointArrived?.Invoke(_currentWaypointIndex);
        WaitStarted?.Invoke(_waitTimeCounter);
    }

    private void InitializeJourneyToNextWaypoint()
    {
        // Validate current waypoint index
        if (_currentWaypointIndex < 0 || _currentWaypointIndex >= waypoints.Count)
        {
            Debug.LogError($"Invalid waypoint index: {_currentWaypointIndex}");
            _isMoving = false;
            return;
        }

        _startPosition = transform.position;
        _targetPosition = waypoints[_currentWaypointIndex].position;
        _journeyLength = Vector3.Distance(_startPosition, _targetPosition);
        _previousWaypointIndex = Mathf.Clamp(_currentWaypointIndex - _directionMultiplier, 0, waypoints.Count - 1);
        _waypointCountCached = waypoints.Count;

        // Calculate duration based on journey length and move speed
        _journeyDuration = _journeyLength / moveSpeed;

        // Reset time tracking
        _currentTime = 0f;
        _journeyProgress = 0f;

        Debug.Log($"Starting journey to waypoint {_currentWaypointIndex} with direction {_directionMultiplier}");
        SegmentInitialized?.Invoke(_currentWaypointIndex, _startPosition, _targetPosition, _journeyDuration);
    }

    public void StartMoving()
    {
        if (waypoints.Count < 2)
        {
            Debug.LogWarning("Cannot start: Need at least 2 waypoints");
            _debugStatus = "Error: Not enough waypoints";
            return;
        }

        if (PersistenceManager.IsRestoring)
        {
            // Allow state to be set but don't actually advance until restoration completes
            _isMoving = true;
            _isWaiting = false;
            _debugStatus = "Queued movement (restoring)";
            return;
        }

        // Check if we need to reverse direction based on current position
        if (_currentWaypointIndex == 0 && _directionMultiplier == -1)
        {
            // At first waypoint going backward - reverse to go forward
            _directionMultiplier = 1;
            Debug.Log("Reversed direction at first waypoint");
        }
        else if (_currentWaypointIndex == waypoints.Count - 1 && _directionMultiplier == 1)
        {
            // At last waypoint going forward - reverse to go backward
            _directionMultiplier = -1;
            Debug.Log("Reversed direction at last waypoint");
        }

        _isMoving = true;
        _isWaiting = false;
        _debugStatus = "Starting movement";

        // Initialize first journey segment
        InitializeJourneyToNextWaypoint();
        MovementStarted?.Invoke();

        // Notify debug display
        if (debugDisplay != null)
        {
            debugDisplay.UpdatePlatformDebug(
                "Starting movement",
                _currentWaypointIndex,
                waypoints.Count - 1,
                _directionMultiplier,
                motionType.ToString(),
                curvePower
            );
        }
    }

    public void StopMoving()
    {
        _isMoving = false;
        _isWaiting = false;
        _debugStatus = "Stopped by command";
        MovementStopped?.Invoke();

        // Clear debug display
        if (debugDisplay != null)
        {
            debugDisplay.ClearPlatformDebug();
        }
    }

    public void ToggleMovement()
    {
        if (!_isMoving)
            StartMoving();
        else
            StopMoving();
    }

    public void ReverseDirection()
    {
        _directionMultiplier *= -1;
        Debug.Log($"Direction manually reversed to {_directionMultiplier}");
        DirectionChanged?.Invoke(_directionMultiplier);

        // If we're already moving, reinitialize the journey with the new direction
        if (_isMoving && !_isWaiting)
        {
            InitializeJourneyToNextWaypoint();
        }
    }

    // Optimization API used by KinematicPlatformManager
    public void SetSleepMode(bool sleep)
    {
        _sleepMode = sleep;
    }

    public void SetHoldForPlayerRestore(bool hold)
    {
        _holdForPlayerRestore = hold;
    }

    // Dedicated attachment anchor for items - creates a child object with identity transform
    private Transform _attachmentAnchor;
    public Transform AttachmentAnchor 
    { 
        get 
        {
            if (_attachmentAnchor == null)
            {
                // Reuse existing anchor if present to avoid duplicates across play sessions/domain reloads
                var existing = transform.Find("PassengerAnchor");
                if (existing != null)
                {
                    _attachmentAnchor = existing;
                }
                else
                {
                    CreateAttachmentAnchor();
                }
            }
            return _attachmentAnchor;
        } 
    }

    private void CreateAttachmentAnchor()
    {
        // Double-check we don't create duplicates if called concurrently
        var existing = transform.Find("PassengerAnchor");
        if (existing != null)
        {
            _attachmentAnchor = existing;
            return;
        }
        GameObject anchorObj = new GameObject("PassengerAnchor");
        anchorObj.transform.SetParent(transform);
        // Ensure perfect identity transform relative to parent
        anchorObj.transform.localPosition = Vector3.zero;
        anchorObj.transform.localRotation = Quaternion.identity;
        anchorObj.transform.localScale = Vector3.one;
        _attachmentAnchor = anchorObj.transform;
        
        // If platform has non-uniform scale, compensate by setting world scale to (1,1,1)
        if (transform.localScale != Vector3.one)
        {
            Vector3 platformScale = transform.lossyScale;
            anchorObj.transform.localScale = new Vector3(
                1f / platformScale.x,
                1f / platformScale.y,
                1f / platformScale.z
            );
        }
    }

    public void AddCurrentPositionAsWaypoint()
    {
        PlatformWaypoint newPoint = new PlatformWaypoint
        {
            position = transform.position,
            waitTime = 1f
        };
        waypoints.Add(newPoint);
        _debugStatus = $"Added waypoint at position {transform.position}";
    }

    public void UpdateWaypointsFromReferences()
    {
        foreach (var waypoint in waypoints)
        {
            waypoint.UpdateFromReference();
        }
    }

    private void OnDrawGizmos()
    {
        if (waypoints == null || waypoints.Count < 2)
            return;

        Gizmos.color = Color.blue;
        for (int i = 0; i < waypoints.Count - 1; i++)
        {
            if (waypoints[i] != null)
            {
                Gizmos.DrawWireSphere(waypoints[i].position, 0.3f);
                if (waypoints[i + 1] != null)
                {
                    Gizmos.DrawLine(waypoints[i].position, waypoints[i + 1].position);
                }
            }
        }

        if (waypoints[waypoints.Count - 1] != null)
        {
            Gizmos.DrawWireSphere(waypoints[waypoints.Count - 1].position, 0.3f);
            if (isLooping && waypoints[0] != null)
            {
                Gizmos.color = Color.blue * 0.5f;
                Gizmos.DrawLine(waypoints[waypoints.Count - 1].position, waypoints[0].position);
            }
        }

        if (Application.isPlaying && _currentWaypointIndex < waypoints.Count)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(waypoints[_currentWaypointIndex].position, 0.4f);

            if (_isMoving && !_isWaiting)
            {
                // Draw path to current target
                Gizmos.color = Color.green;
                Gizmos.DrawLine(transform.position, _targetPosition);

                // Visualize curve
                DrawCurvePath(_startPosition, _targetPosition);
            }
        }
    }

    private void DrawCurvePath(Vector3 start, Vector3 end)
    {
        const int segments = 20;
        Vector3 prevPoint = start;

        for (int i = 1; i <= segments; i++)
        {
            float t = (float)i / segments;
            float curvedT = ApplyMotionCurve(t);
            Vector3 point = Vector3.Lerp(start, end, curvedT);

            // Change color based on segment to visualize acceleration/deceleration
            if (i <= segments / 3)
                Gizmos.color = Color.red; // Start (might be slow for ease-in)
            else if (i <= 2 * segments / 3)
                Gizmos.color = Color.yellow; // Middle (usually faster)
            else
                Gizmos.color = Color.green; // End (might be slow for ease-out)

            Gizmos.DrawLine(prevPoint, point);
            prevPoint = point;
        }
    }

    // Spline helpers when using curved motion between waypoints
    [Header("Spline Movement (Optional)")]
    [SerializeField] private bool useSplineMovement = false;
    private int _previousWaypointIndex = 0;
    private int _waypointCountCached = 0;

    private Vector3 CatmullRom(Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3, float t)
    {
        float t2 = t * t;
        float t3 = t2 * t;
        return 0.5f * (
            (2f * p1) +
            (-p0 + p2) * t +
            (2f * p0 - 5f * p1 + 4f * p2 - p3) * t2 +
            (-p0 + 3f * p1 - 3f * p2 + p3) * t3
        );
    }

    private string GenerateDefaultPlatformId()
    {
        string sceneName = gameObject.scene.name;
        string guid8 = System.Guid.NewGuid().ToString("N").Substring(0, 8).ToUpperInvariant();
        return $"KP_{sceneName}_{guid8}";
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(KinematicPlatform))]
public class KinematicPlatformEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        KinematicPlatform platform = (KinematicPlatform)target;

        if (serializedObject.FindProperty("showDebugControls").boolValue)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Debug Controls", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Add Current Position"))
            {
                platform.AddCurrentPositionAsWaypoint();
            }
            if (GUILayout.Button("Update From References"))
            {
                platform.UpdateWaypointsFromReferences();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Start Moving"))
            {
                platform.StartMoving();
            }
            if (GUILayout.Button("Stop Moving"))
            {
                platform.StopMoving();
            }
            if (GUILayout.Button("Reverse"))
            {
                platform.ReverseDirection();
            }
            EditorGUILayout.EndHorizontal();
        }
    }
}
#endif
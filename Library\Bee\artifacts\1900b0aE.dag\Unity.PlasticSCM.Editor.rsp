-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.ref.dll"
-define:UNITY_6000_2_0
-define:UNITY_6000_2
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:UNITY_6000_2_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_UNITY_CONSENT
-define:ENABLE_UNITY_CLOUD_IDENTIFIERS
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLOUD_SERVICES_ENGINE_DIAGNOSTICS
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:MIRROR
-define:MIRROR_81_OR_NEWER
-define:MIRROR_82_OR_NEWER
-define:MIRROR_83_OR_NEWER
-define:MIRROR_84_OR_NEWER
-define:MIRROR_85_OR_NEWER
-define:MIRROR_86_OR_NEWER
-define:MIRROR_89_OR_NEWER
-define:MIRROR_90_OR_NEWER
-define:MIRROR_93_OR_NEWER
-define:EDGEGAP_PLUGIN_SERVERS
-define:RealtimeCSG
-define:RealtimeCSG_1
-define:RealtimeCSG_1_6
-define:RealtimeCSG_1_6_01
-define:BAKERY_INCLUDED
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-define:UNITY_EDITOR_ONLY_COMPILATION
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.ClothModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AMDModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.IdentifiersModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InsightsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConsentModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/ApplicationDataPath.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssemblyInfo.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetMenu/AssetCopyPathOperation.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetMenu/AssetFilesFilterPatternsMenuBuilder.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetMenu/AssetMenuItems.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetMenu/AssetMenuOperations.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetMenu/AssetsSelection.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetMenu/AssetVcsOperations.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetMenu/Dialogs/CheckinDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetMenu/Dialogs/CheckinDialogOperations.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetMenu/ProjectViewAssetSelection.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetOverlays/AssetStatus.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetOverlays/Cache/AssetStatusCache.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetOverlays/Cache/BuildPathDictionary.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetOverlays/Cache/LocalStatusCache.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetOverlays/Cache/LockStatusCache.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetOverlays/Cache/RemoteStatusCache.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetOverlays/DrawAssetOverlay.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetsUtils/AssetsPath.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetsUtils/GetSelectedPaths.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetsUtils/LoadAsset.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetsUtils/Processor/AssetModificationProcessor.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetsUtils/Processor/AssetPostprocessor.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetsUtils/Processor/AssetsProcessor.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetsUtils/Processor/PlasticAssetsProcessor.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetsUtils/Processor/UnityCloudProjectLinkMonitor.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetsUtils/Processor/WorkspaceOperationsMonitor.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetsUtils/ProjectPath.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetsUtils/RefreshAsset.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetsUtils/RepaintInspector.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AssetsUtils/SaveAssets.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/AutoRefresh.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/BuildGetEventExtraInfoFunction.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/CheckWorkspaceTreeNodeStatus.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/AutoConfig.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/ChannelCertificateUiImpl.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/ClientConfiguration.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/CloudEdition/Welcome/AutoLogin.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/CloudEdition/Welcome/CloudEditionWelcomeWindow.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/CloudEdition/Welcome/OrganizationPanel.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/CloudEdition/Welcome/SignInPanel.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/CloudEdition/Welcome/SignInWithEmailPanel.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/CloudEdition/Welcome/WaitingSignInPanel.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/ConfigurePartialWorkspace.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/CredentialsDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/CredentialsUIImpl.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/EncryptionConfigurationDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/MissingEncryptionPasswordPromptHandler.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/SSOCredentialsDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/TeamEdition/TeamEditionConfigurationWindow.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/ToolConfig.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Configuration/WriteLogConfiguration.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Developer/CheckinProgress.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Developer/GenericProgress.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Developer/IncomingChangesNotification.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Developer/ProgressOperationHandler.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Developer/ShelvedChangesNotification.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Developer/UpdateProgress.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Developer/UpdateReport/UpdateReportDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Developer/UpdateReport/UpdateReportLineListViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Developer/UpdateReport/UpdateReportListHeaderState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Developer/UpdateReport/UpdateReportListView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/DrawGuiModeSwitcher.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/EnumExtensions.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/ExternalLink.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/FindWorkspace.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/GetRelativePath.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Gluon/CheckinProgress.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Gluon/Errors/ErrorListViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Gluon/Errors/ErrorsListHeaderState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Gluon/Errors/ErrorsListView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Gluon/Errors/ErrorsPanel.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Gluon/IncomingChangesNotification.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Gluon/ProgressOperationHandler.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Gluon/ShelvedChangesNotification.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Gluon/UpdateProgress.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Gluon/UpdateReport/ErrorListViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Gluon/UpdateReport/UpdateReportDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Gluon/UpdateReport/UpdateReportListHeaderState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Gluon/UpdateReport/UpdateReportListView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Hub/CommandLineArguments.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Hub/Operations/CreateWorkspace.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Hub/Operations/DownloadRepository.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Hub/Operations/OperationParams.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Hub/ParseArguments.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Hub/ProcessHubCommand.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Inspector/DrawInspectorOperations.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Inspector/InspectorAssetSelection.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/MetaPath.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/NewIncomingChanges.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/OrganizationsInformation.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/ParentWindow.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/PlasticApp.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/PlasticConnectionMonitor.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/PlasticMenuItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/PlasticNotification.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/PlasticPlugin.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/PlasticPluginIsEnabledPreference.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/PlasticShutdown.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/PlasticWindow.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/ProjectPackages.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/ProjectWindow.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/QueryVisualElementsExtensions.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/SceneView/DrawSceneOperations.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Settings/DiffAndMergeOptionsFoldout.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Settings/OpenPlasticProjectSettings.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Settings/OtherOptionsFoldout.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Settings/PendingChangesOptionsFoldout.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Settings/PlasticProjectSettingsProvider.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Settings/ShelveAndSwitchOptionsFoldout.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/SwitchModeConfirmationDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Tool/AuthToken.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Tool/BringWindowToFront.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Tool/FindTool.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Tool/IsExeAvailable.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Tool/IsExeVersion.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Tool/LaunchInstaller.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Tool/LaunchTool.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Tool/PlasticExeLauncher.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Tool/ToolConstants.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Avatar/ApplyCircleMask.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Avatar/AvatarImages.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Avatar/GetAvatar.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/BoolSetting.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/CloseWindowIfOpened.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/CooldownWindowDelayer.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/DialogWithCheckBox.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/DockEditorWindow.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/DrawActionButton.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/DrawActionButtonWithMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/DrawActionHelpBox.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/DrawActionToolbar.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/DrawCopyableLabel.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/DrawSearchField.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/DrawSplitter.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/DrawStaticElement.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/DrawTextBlockWithLink.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/DrawUserIcon.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/DropDownTextField.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/EditorDispatcher.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/EditorProgressBar.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/EditorProgressControls.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/EditorVersion.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/EditorWindowFocus.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/EnumPopupSetting.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/FindEditorWindow.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/GetPlasticShortcut.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/GetWindowIfOpened.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/GUIActionRunner.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/GuiEnabled.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/HandleMenuItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Images.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/MeasureMaxWidth.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/OverlayRect.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/PlasticDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/PlasticSplitterGUILayout.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Progress/DrawProgressForDialogs.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Progress/DrawProgressForOperations.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Progress/DrawProgressForViews.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Progress/OperationProgressData.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Progress/ProgressControlsForDialogs.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Progress/ProgressControlsForViews.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/ResponseType.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/RunModal.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/ScreenResolution.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/ShowWindow.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/SortOrderComparer.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/StatusBar/GUIContentNotification.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/StatusBar/INotificationContent.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/StatusBar/NotificationBar.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/StatusBar/StatusBar.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/TabButton.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Tree/DrawTreeViewEmptyState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Tree/DrawTreeViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Tree/GetChangesOverlayIcon.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Tree/ListViewItemIds.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Tree/PlasticTreeView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Tree/TableViewOperations.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Tree/TreeHeaderColumns.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Tree/TreeHeaderSettings.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Tree/TreeViewItemExtensions.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Tree/TreeViewItemIds.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/Tree/TreeViewSessionState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/UIElements/LoadingSpinner.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/UIElements/ProgressControlsForDialogs.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/UIElements/UIElementsExtensions.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/UnityConstants.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/UnityEvents.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/UnityMenuItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/UnityPlasticGuiMessage.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/UnityPlasticTimer.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/UnityStyles.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UI/UnityThreadWaiter.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UnityConfigurationChecker.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/UVCPackageVersion.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/VCSPlugin.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/ApplyShelveWithConflictsQuestionerBuilder.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Branch/BranchesListHeaderState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Branch/BranchesListView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Branch/BranchesSelection.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Branch/BranchesTab.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Branch/BranchesTab_Operations.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Branch/BranchesViewMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Branch/BranchListViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Branch/Dialogs/CreateBranchDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Branch/Dialogs/DeleteBranchDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Branch/Dialogs/RenameBranchDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Branch/SerializableBranchesTabState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Changesets/ChangesetListViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Changesets/ChangesetsListHeaderState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Changesets/ChangesetsListView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Changesets/ChangesetsSelection.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Changesets/ChangesetsTab.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Changesets/ChangesetsTab_Operations.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Changesets/ChangesetsViewMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Changesets/DateFilter.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Changesets/LaunchDiffOperations.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/ConfirmContinueWithPendingChangesDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/CreateWorkspace/CreateWorkspaceView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/CreateWorkspace/CreateWorkspaceViewState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/CreateWorkspace/Dialogs/CreateRepositoryDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/CreateWorkspace/Dialogs/RepositoriesListHeaderState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/CreateWorkspace/Dialogs/RepositoriesListView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/CreateWorkspace/Dialogs/RepositoryExplorerDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/CreateWorkspace/Dialogs/RepositoryListViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/CreateWorkspace/DrawCreateWorkspaceView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/CreateWorkspace/PerformInitialCheckin.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/CreateWorkspace/ValidRepositoryName.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Diff/ChangeCategoryTreeViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Diff/ClientDiffTreeViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Diff/Dialogs/GetRestorePathDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Diff/DiffPanel.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Diff/DiffSelection.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Diff/DiffTreeView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Diff/DiffTreeViewMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Diff/GetClientDiffInfos.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Diff/MergeCategoryTreeViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Diff/UnityDiffTree.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/DownloadPlasticExeDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/EnableSwitchAndShelveFeatureDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/FileSystemOperation.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/History/HistoryListHeaderState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/History/HistoryListView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/History/HistoryListViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/History/HistoryListViewMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/History/HistorySelection.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/History/HistoryTab.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/History/SaveAction.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/History/SerializableHistoryTabState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Locks/DrawLocksListViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Locks/LocksListHeaderState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Locks/LocksListView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Locks/LocksListViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Locks/LocksSelector.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Locks/LocksTab.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Locks/LocksViewMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/ChangeCategoryTreeViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/ChangeTreeViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/AddMoveMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/ChangeDeleteMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/ConflictResolutionState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/CycleMoveMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/DeleteChangeMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/DeleteMoveMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/DivergentMoveMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/DrawDirectoryResolutionPanel.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/EvilTwinMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/LoadedTwiceMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/MergeViewDirectoryConflictMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/MoveAddMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/MoveDeleteMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/DirectoryConflicts/MovedEvilTwinMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/IsCurrent.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/IsResolved.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/MergeOptionsDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/MergeSelection.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/MergeTab.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/MergeTreeHeaderState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/MergeTreeView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/MergeViewFileConflictMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/MergeViewMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/SerializableMergeTabState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Developer/UnityMergeTree.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/DrawMergeOverview.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Gluon/ChangeCategoryTreeViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Gluon/ChangeTreeViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Gluon/IncomingChangesSelection.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Gluon/IncomingChangesTab.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Gluon/IncomingChangesTreeHeaderState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Gluon/IncomingChangesTreeView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Gluon/IncomingChangesViewMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/Gluon/UnityIncomingChangesTree.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Merge/IIncomingChangesTab.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/ChangeCategoryTreeViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/Changelists/ChangelistMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/Changelists/MoveToChangelistMenuBuilder.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/ChangelistTreeViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/ChangeTreeViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/CreatedChangesetData.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/Dialogs/CheckinConflictsDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/Dialogs/CreateChangelistDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/Dialogs/DependenciesDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/Dialogs/EmptyCommentDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/Dialogs/FilterRulesConfirmationDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/Dialogs/LaunchCheckinConflictsDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/Dialogs/LaunchDependenciesDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/DrawCommentTextArea.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/DrawOperationSuccess.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/DrawPendingChangesEmptyState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/FilesFilterPatternsMenuBuilder.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/PendingChangesMultiColumnHeader.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/PendingChangesSelection.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/PendingChangesStatusSuccessNotificationContent.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/PendingChangesTab.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/PendingChangesTab_Operations.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/PendingChangesTreeHeaderState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/PendingChangesTreeView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/PendingChangesViewMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/PendingChangesViewPendingChangeMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/PendingMergeLinks/MergeLinkListViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/PendingMergeLinks/MergeLinksListView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/PendingChanges/UnityPendingChangesTree.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/SelectNewCodeReviewBehavior.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/ShelvePendingChangesDialog.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Shelves/ShelveListViewItem.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Shelves/ShelvesListHeaderState.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Shelves/ShelvesListView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Shelves/ShelvesSelection.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Shelves/ShelvesTab.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Shelves/ShelvesViewMenu.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Welcome/DownloadAndInstallOperation.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Welcome/GetInstallerTmpFileName.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Welcome/MacOSConfigWorkaround.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/Views/Welcome/WelcomeView.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/ViewSwitcher.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/VisualElementExtensions.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/WebApi/CredentialsResponse.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/WebApi/CurrentUserAdminCheckResponse.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/WebApi/ErrorResponse.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/WebApi/SubscriptionDetailsResponse.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/WebApi/TokenExchangeResponse.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/WebApi/WebRestApiClient.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/WorkspaceWindow.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/_Deprecated/CollabMigration/MigrateCollabProject.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/_Deprecated/CollabPlugin.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/_Deprecated/WebApi/ChangesetFromCollabCommitResponse.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/_Deprecated/WebApi/IsCollabProjectMigratedResponse.cs"
"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Editor/_Deprecated/WebApi/OrganizationCredentials.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"
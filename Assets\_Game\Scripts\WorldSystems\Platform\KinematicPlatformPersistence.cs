using System;
using UnityEngine;

/// <summary>
/// Persistence component for KinematicPlatform. Mirrors HeavyObjectManager pattern but
/// uses platform-specific state instead of generic physics snapshots.
/// </summary>
[DisallowMultipleComponent]
public class KinematicPlatformPersistence : MonoBehaviour
{
    [Header("Platform Persistence Settings")]
    [SerializeField] private string objectName = "";
    [SerializeField] private string category = "Platforms";
    [Tooltip("Optional stable identifier. If set, this will be used in the unique id.")]
    [SerializeField] private string customId = "";

    [Header("State (Read Only)")]
    [SerializeField] private string uniqueId = "";
    [SerializeField] private string persistentGuid = ""; // stable, human-agnostic
    [SerializeField] private bool isRegistered = false;
    [SerializeField] private bool pausedByButton = false;

    private KinematicPlatform _platform;
    private Rigidbody _rb;
    // Cached delegates for proper unsubscribe
    private Action _onMoveStarted;
    private Action _onMoveStopped;
    private Action<float> _onWaitStarted;
    private Action _onWaitEnded;
    private Action<int> _onWaypointArrived;
    private Action<int, Vector3, Vector3, float> _onSegmentInitialized;
    private Action<int> _onDirectionChanged;

    public string UniqueId => uniqueId;
    public string ObjectName => string.IsNullOrEmpty(objectName) ? gameObject.name : objectName;
    public KinematicPlatform Platform => _platform;
    public void SetPausedByButton(bool paused) { pausedByButton = paused; }

    private void Awake()
    {
        _platform = GetComponent<KinematicPlatform>();
        _rb = GetComponent<Rigidbody>();
        if (string.IsNullOrEmpty(uniqueId))
        {
            GenerateUniqueId();
        }
    }

#if UNITY_EDITOR
    private void OnValidate()
    {
        if (!Application.isPlaying)
        {
            if (string.IsNullOrEmpty(persistentGuid))
            {
                persistentGuid = System.Guid.NewGuid().ToString("N").ToUpperInvariant();
            }
            if (string.IsNullOrEmpty(uniqueId))
            {
                uniqueId = $"KP_{persistentGuid}";
            }
        }
    }
#endif

    private void OnEnable()
    {
        RegisterWithPersistence();
        WireEvents(true);
    }

    private void OnDisable()
    {
        WireEvents(false);
        UnregisterWithPersistence();
    }

    private void OnDestroy()
    {
        WireEvents(false);
        UnregisterWithPersistence();
    }

    private void WireEvents(bool subscribe)
    {
        if (_platform == null) return;
        if (subscribe)
        {
            _onMoveStarted = HandlePlatformStateChanged;
            _onMoveStopped = HandlePlatformStateChanged;
            _onWaitStarted = OnWaitStarted;
            _onWaitEnded = HandlePlatformStateChanged;
            _onWaypointArrived = OnWaypointArrived;
            _onSegmentInitialized = OnSegmentInitialized;
            _onDirectionChanged = OnDirectionChanged;

            _platform.MovementStarted += _onMoveStarted;
            _platform.MovementStopped += _onMoveStopped;
            _platform.WaitStarted += _onWaitStarted;
            _platform.WaitEnded += _onWaitEnded;
            _platform.WaypointArrived += _onWaypointArrived;
            _platform.SegmentInitialized += _onSegmentInitialized;
            _platform.DirectionChanged += _onDirectionChanged;
        }
        else
        {
            if (_onMoveStarted != null) _platform.MovementStarted -= _onMoveStarted;
            if (_onMoveStopped != null) _platform.MovementStopped -= _onMoveStopped;
            if (_onWaitStarted != null) _platform.WaitStarted -= _onWaitStarted;
            if (_onWaitEnded != null) _platform.WaitEnded -= _onWaitEnded;
            if (_onWaypointArrived != null) _platform.WaypointArrived -= _onWaypointArrived;
            if (_onSegmentInitialized != null) _platform.SegmentInitialized -= _onSegmentInitialized;
            if (_onDirectionChanged != null) _platform.DirectionChanged -= _onDirectionChanged;
        }
    }

    private void OnWaitStarted(float _)
    {
        HandlePlatformStateChanged();
    }
    private void OnWaypointArrived(int _)
    {
        HandlePlatformStateChanged();
    }
    private void OnSegmentInitialized(int _, Vector3 __, Vector3 ___, float ____)
    {
        HandlePlatformStateChanged();
    }
    private void OnDirectionChanged(int _)
    {
        HandlePlatformStateChanged();
    }

    private void HandlePlatformStateChanged()
    {
        // If movement has started by any means, clear the paused-by-button flag
        if (_platform != null && _platform.IsMoving && pausedByButton)
        {
            pausedByButton = false;
        }

        var pm = PersistenceManager.Instance;
        if (pm != null)
        {
            pm.OnKinematicPlatformStateChanged(this);
        }
    }

    private void RegisterWithPersistence()
    {
        if (isRegistered) return;
        var pm = PersistenceManager.Instance;
        if (pm != null)
        {
            pm.RegisterKinematicPlatform(this);
            isRegistered = true;
        }
    }

    private void UnregisterWithPersistence()
    {
        if (!isRegistered) return;
        var pm = PersistenceManager.Instance;
        if (pm != null)
        {
            pm.UnregisterKinematicPlatform(this);
            isRegistered = false;
        }
    }

    private void GenerateUniqueId()
    {
        if (!string.IsNullOrEmpty(customId))
        {
            uniqueId = $"KP_{customId}";
            return;
        }

        if (string.IsNullOrEmpty(persistentGuid))
        {
            persistentGuid = System.Guid.NewGuid().ToString("N").ToUpperInvariant();
        }
        uniqueId = $"KP_{persistentGuid}";
    }

    private string BuildHierarchyPath(Transform t)
    {
        System.Text.StringBuilder sb = new System.Text.StringBuilder();
        var nodes = new System.Collections.Generic.Stack<string>();
        Transform c = t;
        while (c != null)
        {
            nodes.Push(c.name);
            c = c.parent;
        }
        while (nodes.Count > 0)
        {
            var name = nodes.Pop();
            if (sb.Length > 0) sb.Append('/');
            sb.Append(name);
        }
        return sb.ToString();
    }

    public PersistenceManager.KinematicPlatformData GetSaveData()
    {
        var data = new PersistenceManager.KinematicPlatformData();
        data.id = uniqueId;
        data.objectName = ObjectName;
        data.sceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;

        if (_platform != null)
        {
            data.currentWaypointIndex = _platform.CurrentWaypointIndex;
            data.directionMultiplier = _platform.DirectionMultiplier;
            data.isMoving = _platform.IsMoving;
            data.isWaiting = _platform.IsWaiting;
            data.waitTimeRemaining = _platform.WaitTimeRemaining;
            data.currentTime = _platform.CurrentTime;
            data.journeyDuration = _platform.JourneyDuration;
            data.startPosition = _platform.StartPosition;
            data.targetPosition = _platform.TargetPosition;
            data.motionType = _platform.MotionType;
            data.curvePower = _platform.CurvePower;
            data.moveSpeed = _platform.MoveSpeed;
            data.isLooping = _platform.IsLooping;
            data.currentWorldPosition = transform.position;
            data.pausedByButton = pausedByButton;
        }

        return data;
    }

    public void RestoreFromSaveData(PersistenceManager.KinematicPlatformData data)
    {
        if (data == null || _platform == null) return;

        // If saved state suggests we were mid-segment but flags were inconsistent, prefer deriving from saved world position
        bool savedHasValidSegment = data.journeyDuration > 0f && (data.startPosition - data.targetPosition).sqrMagnitude > 1e-6f;
        bool savedWasMidSegment = savedHasValidSegment && data.currentTime > 0f && data.currentTime < data.journeyDuration;

        // Apply config first
        _platform.ApplyPersistenceState(
            data.currentWaypointIndex,
            data.directionMultiplier,
            data.isMoving,
            data.isWaiting,
            data.waitTimeRemaining,
            data.currentTime,
            data.journeyDuration,
            data.startPosition,
            data.targetPosition,
            data.motionType,
            data.curvePower,
            data.moveSpeed,
            data.isLooping);

        // If platform snapped to waypoint due to inconsistent flags, try to re-derive continuous state from saved world position
        // Prefer world position when it lies between start/target (not near endpoints)
        if (savedHasValidSegment)
        {
            int current = _platform.CurrentWaypointIndex;
            int prev = Mathf.Clamp(current - _platform.DirectionMultiplier, 0, _platform.WaypointCount - 1);
            Vector3 start = _platform.GetWaypointPosition(prev);
            Vector3 target = _platform.GetWaypointPosition(current);
            Vector3 seg = target - start;
            float segLen2 = seg.sqrMagnitude;
            if (segLen2 > 1e-6f)
            {
                float t = Mathf.Clamp01(Vector3.Dot(data.currentWorldPosition - start, seg) / segLen2);
                float distToStart = Vector3.Distance(data.currentWorldPosition, start);
                float distToTarget = Vector3.Distance(data.currentWorldPosition, target);
                float tol = Mathf.Max(0.05f, _platform.PositionTolerance * 4f);
                bool isClearlyMidSegment = t > 0.02f && t < 0.98f && distToStart > tol && distToTarget > tol;
                if (isClearlyMidSegment)
                {
                    _platform.ApplyDerivedStateFromWorldPosition(data.currentWorldPosition);
                }
            }
        }

        // If the saved data indicates the player explicitly paused it, enforce stopped state after applying base state
        pausedByButton = data.pausedByButton;
        if (pausedByButton) { _platform.StopMoving(); }

        // Ensure rigidbody kinematic stays and velocity continuity from PhysicsMover
        if (_rb != null)
        {
            _rb.isKinematic = true;
            // Snap RB to current transform to avoid interpolation popping on load
            _rb.position = transform.position;
            _rb.rotation = transform.rotation;
        }
        
    }
}



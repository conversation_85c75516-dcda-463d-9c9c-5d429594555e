using System.Collections.Generic;
using UnityEngine;
using KinematicCharacterController.FPS;

public class KinematicPlatformManager : MonoBehaviour
{
    private static KinematicPlatformManager _instance;
    public static KinematicPlatformManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindFirstObjectByType<KinematicPlatformManager>();
                if (_instance == null)
                {
                    GameObject go = new GameObject("KinematicPlatformManager");
                    _instance = go.AddComponent<KinematicPlatformManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return _instance;
        }
    }

    private readonly List<KinematicPlatform> allPlatforms = new List<KinematicPlatform>();
    private readonly List<KinematicPlatform> activePlatforms = new List<KinematicPlatform>();

    [Header("Optimization Settings")]
    [SerializeField] private float platformUpdateDistance = 100f;
    [SerializeField] private int maxPlatformsPerFrame = 10;

    private Transform playerTransform;
    private int currentUpdateIndex = 0;

    public void RegisterPlatform(KinematicPlatform platform)
    {
        if (platform == null) return;
        if (!allPlatforms.Contains(platform))
        {
            allPlatforms.Add(platform);
        }
    }

    private void OnEnable()
    {
        // Discover any platforms already in the scene and register them
        var found = FindObjectsByType<KinematicPlatform>(FindObjectsSortMode.None);
        for (int i = 0; i < found.Length; i++)
        {
            RegisterPlatform(found[i]);
        }
    }

    public void UnregisterPlatform(KinematicPlatform platform)
    {
        allPlatforms.Remove(platform);
        activePlatforms.Remove(platform);
    }

    private void Start()
    {
        var player = FindFirstObjectByType<FPSCharacterController>();
        if (player != null) playerTransform = player.transform;
    }

    private void Update()
    {
        if (playerTransform == null) return;

        activePlatforms.Clear();
        Vector3 playerPos = playerTransform.position;

        float maxDistSqr = platformUpdateDistance * platformUpdateDistance;
        for (int i = 0; i < allPlatforms.Count; i++)
        {
            var platform = allPlatforms[i];
            if (platform == null) continue;
            float distSqr = (platform.transform.position - playerPos).sqrMagnitude;
            if (distSqr <= maxDistSqr)
            {
                activePlatforms.Add(platform);
            }
            else
            {
                platform.SetSleepMode(true);
            }
        }

        int processed = 0;
        int startIndex = currentUpdateIndex;
        int activeCount = activePlatforms.Count;
        while (processed < maxPlatformsPerFrame && processed < activeCount)
        {
            int index = (startIndex + processed) % activeCount;
            var platform = activePlatforms[index];
            if (platform != null)
            {
                platform.SetSleepMode(false);
            }
            processed++;
        }

        currentUpdateIndex = activeCount == 0 ? 0 : (currentUpdateIndex + processed) % activeCount;
    }
}



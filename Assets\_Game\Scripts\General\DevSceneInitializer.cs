using UnityEngine;

#if UNITY_EDITOR
public class DevSceneInitializer : MonoBehaviour
{
    void Awake()
    {
        // Only do this if we're in Main scene and there's no GameFlowManager
        if (UnityEngine.SceneManagement.SceneManager.GetActiveScene().name == "Main" && GameFlowManager.Instance == null)
        {
            Debug.Log("Dev mode: Initializing GameFlowManager for direct scene testing");
            
            // First check if there's existing save data we want to keep
            string savePath = System.IO.Path.Combine(Application.persistentDataPath, "gamesave.json");
            if (!System.IO.File.Exists(savePath))
            {
                Debug.Log("Dev mode: No existing save found, creating default save data");
                // Create GameFlowManager
                var go = new GameObject("GameFlowManager");
                var manager = go.AddComponent<GameFlowManager>();
                DontDestroyOnLoad(go);
            }
            else
            {
                Debug.Log("Dev mode: Found existing save data, creating GameFlowManager without overwriting");
                var go = new GameObject("GameFlowManager");
                var manager = go.AddComponent<GameFlowManager>();
                DontDestroyOnLoad(go);
            }
        }
    }
}
#endif
-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.ref.dll"
-define:UNITY_6000_2_0
-define:UNITY_6000_2
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:UNITY_6000_2_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_UNITY_CONSENT
-define:ENABLE_UNITY_CLOUD_IDENTIFIERS
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLOUD_SERVICES_ENGINE_DIAGNOSTICS
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:MIRROR
-define:MIRROR_81_OR_NEWER
-define:MIRROR_82_OR_NEWER
-define:MIRROR_83_OR_NEWER
-define:MIRROR_84_OR_NEWER
-define:MIRROR_85_OR_NEWER
-define:MIRROR_86_OR_NEWER
-define:MIRROR_89_OR_NEWER
-define:MIRROR_90_OR_NEWER
-define:MIRROR_93_OR_NEWER
-define:EDGEGAP_PLUGIN_SERVERS
-define:RealtimeCSG
-define:RealtimeCSG_1
-define:RealtimeCSG_1_6
-define:RealtimeCSG_1_6_01
-define:BAKERY_INCLUDED
-define:HDRP_1_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-define:UNITY_EDITOR_ONLY_COMPILATION
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.ClothModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AMDModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.IdentifiersModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InsightsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConsentModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d78732e851eb/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.testtools.codecoverage@205a02cbcb39/lib/ReportGenerator/ReportGeneratorMerged.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Analytics/AssetReimporterAnalytic.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssemblyInfo.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/AssetVersion.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/AutodeskInteractiveMaterialImport.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/CubeLutImporter.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/FBXArnoldSurfaceMaterialDescriptionPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/FBXMaterialDescriptionPostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/HDIESImporterEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/HDRenderPipelineGlobalSettingsPostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/MaterialPostProcessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/ModelPostProcessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/NormalMapFilteringTexturePostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/PhysicalMaterial3DsMaxPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/PluginMaterialVersions.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/ShaderGraphMaterialsUpdater.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/SketchupMaterialDescriptionPostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/AssetProcessors/ThreeDSMaterialDescriptionPostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/BuildProcessors/HDRPBuildData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/BuildProcessors/HDRPBuildDataValidator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/BuildProcessors/HDRPPreprocessBuild.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/BuildProcessors/HDRPPreprocessShaders.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/BuildProcessors/SettingsStrippers/HDRPRayTracingResourcesStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/BuildProcessors/ShaderStrippers/HDRPComputeShaderVariantStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/BuildProcessors/ShaderStrippers/HDRPDisabledComputeShaderVariantStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/BuildProcessors/ShaderStrippers/HDRPShaderVariantStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Compositor/CompositionFilterUI.Drawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Compositor/CompositionLayerUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Compositor/CompositionManagerEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Compositor/CompositionManagerEditor.Styles.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Compositor/CompositionUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Compositor/CompositorWindow.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Compositor/SerializedCompositionFilter.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Compositor/SerializedCompositionLayer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Compositor/SerializedCompositionManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Compositor/SeriallizedShaderProperty.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Compositor/ShaderPropertyUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/ContextualMenuDispatcher.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Core/HDRenderPipelinePreferencesProvider.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Core/TextureCombiner/TextureCombiner.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/HDAnalytics.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/HlslDerivatives/HlslGenerator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/HlslDerivatives/HlslParser.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/HlslDerivatives/HlslProcessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/HlslDerivatives/HlslTokenizer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/HlslDerivatives/HlslTree.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/HlslDerivatives/HlslUnityReserved.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/HlslDerivatives/HlslUtil.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/HlslDerivatives/PartialDerivUtilWriter.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/HlslDerivatives/ShaderTokenUtil.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/AdditionalShadowDataEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/DiffusionProfileListEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/HDAdditionalLightDataEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/HDIESImporter.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/HDLightEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/HDLightExplorerExtension.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/HDLightUI.ContextualMenu.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/HDLightUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/HDLightUI.Handles.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/HDLightUI.PresetInspector.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/HDLightUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/IndirectLightingControllerEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/LightmappingHDRP.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/LightUnit/HDLightUnitSliderUIDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/LightUnit/HDPiecewiseLightUnitSlider.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/CameraSettingsUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/HDAdditionalReflectionDataEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/HDBakedReflectionSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/HDProbeEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/HDProbeUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/HDProbeUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/HDProbeUI.Handles.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/HDProbeUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/HDReflectionProbeEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/HDReflectionProbeEditor.Gizmos.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/HDReflectionProbeEditor.Preview.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/HDReflectionProbeEditor.ProbeUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/HDScreenSpaceReflectionEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/PlanarReflectionProbeEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/ProbeSettingsUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/ReflectionMenuItem.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/ScreenSpaceRefractionEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/SerializedCameraSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/SerializedHDProbe.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/SerializedHDReflectionProbe.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/SerializedPlanarReflectionProbe.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/SerializedProbeSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/Volume/InfluenceVolumeUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/Volume/InfluenceVolumeUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/Volume/InfluenceVolumeUI.Gizmos.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/Volume/InfluenceVolumeUI.Handles.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/Volume/InfluenceVolumeUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/Volume/ProxyVolumeUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/Volume/ReflectionProxyVolumeComponentEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/Volume/SerializedInfluenceVolume.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/Volume/SerializedProxyVolume.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Reflection/Volume/SerializedReflectionProxyVolumeComponent.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/RenderingLayerMaskPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/ScreenSpaceAmbientOcclusionEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/SerializedHDLight.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Shadow/ContactShadowsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Shadow/HDShadowSettingsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/Shadow/MicroShadowingEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/VolumetricClouds/VolumetricCloudsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/VolumetricClouds/VolumetricCloudsResourcesStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/VolumetricClouds/WorleyfBmGenerator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/VolumetricLighting/LocalVolumetricFogEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/VolumetricLighting/LocalVolumetricFogUI.Drawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/VolumetricLighting/LocalVolumetricFogUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/VolumetricLighting/SerializedLocalVolumetricFog.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Lighting/VolumetricLighting/VolumetricMenuItem.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/AxF/AxFGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/BaseShaderPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Canvas/ShaderGraph/CreateHDCanvasShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Canvas/ShaderGraph/HDCanvasData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Canvas/ShaderGraph/HDCanvasSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Decal/DecalMenuItems.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Decal/DecalProjectorEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Decal/DecalProjectorEditor.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Decal/DecalUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Decal/DisplacableRectHandles.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Decal/ProjectedTransform.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Decal/ShaderGraph/CreateDecalShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Decal/ShaderGraph/DecalData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Decal/ShaderGraph/DecalPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Decal/ShaderGraph/DecalShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Decal/ShaderGraph/DecalSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Decal/ShaderGraph/DecalSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/DiffusionProfile/DiffusionProfileMaterialUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/DiffusionProfile/DiffusionProfileSettingsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/DiffusionProfile/DiffusionProfileSettingsEditor.Styles.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/DiffusionProfile/SerializedDiffusionProfileSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/CreateEyeShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/EyeData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/EyeSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/EyeSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/EyeSurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/Node/BuiltinCorneaIOR.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/Node/BuiltinIrisPlaneOffset.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/Node/BuiltinIrisRadius.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/Node/CirclePupilAnimation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/Node/CorneaRefraction.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/Node/EyeSurfaceTypeDebug.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/Node/IrisLimbalRing.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/Node/IrisOffset.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/Node/IrisOutOfBoundColorClamp.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/Node/IrisUVLocation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/Node/ScleraIrisBlend.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/Node/ScleraLimbalRing.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Eye/ShaderGraph/Node/ScleraUVLocation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Fabric/ShaderGraph/CreateFabricShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Fabric/ShaderGraph/FabricData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Fabric/ShaderGraph/FabricSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Fabric/ShaderGraph/FabricSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Fabric/ShaderGraph/FabricSurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/FogVolume/FogVolumePropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/FogVolume/FogVolumeShaderGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/FogVolume/ShaderGraph/CreateFogVolumeShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/FogVolume/ShaderGraph/FogVolumeData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/FogVolume/ShaderGraph/FogVolumeSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/FogVolume/ShaderGraph/FogVolumeUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Fullscreen/ShaderGraph/CreateHDFullscreenShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Fullscreen/ShaderGraph/HDFullscreenData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Fullscreen/ShaderGraph/HDFullscreenSubtarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Hair/ShaderGraph/CreateHairShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Hair/ShaderGraph/HairData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Hair/ShaderGraph/HairPropertyBlocks.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Hair/ShaderGraph/HairSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Hair/ShaderGraph/HairSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/LayeredLit/LayeredLitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Lit/LitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Lit/LitShaderPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Lit/ShaderGraph/CreateHDLitShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Lit/ShaderGraph/HDLitData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Lit/ShaderGraph/HDLitSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Lit/ShaderGraph/HDLitSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Lit/ShaderGraph/LitSurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Lit/StandardsToHDLitMaterialUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/LTCAreaLight/LTC.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/LTCAreaLight/LTCTableGenerator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/LTCAreaLight/LTCTableGeneratorEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/LTCAreaLight/NelderMead.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Nature/HDSpeedTree8MaterialUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Nature/HDSpeedTree9MaterialUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/PBRSky/ShaderGraph/PBRSkySubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/AdvancedOptionsPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/DiffusionProfileMaterialPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/DiffusionProfilePropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/DiffusionProfileShaderProperty.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/DistortionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/HDBlockFields.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/HDFields.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/HDMetadata.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/HDPropertiesHeader.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/HDShaderKernels.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/HDShaderPasses.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/HDStructFields.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/HDStructs.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/HDSubShaderUtilities.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/HDSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/HDTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/IPluginSubTargetMaterialUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Legacy/DecalMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Legacy/EyeMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Legacy/FabricMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Legacy/HairMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Legacy/HDLitMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Legacy/HDUnlitMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Legacy/StackLitMasterNode1.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/LightingSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Nodes/CustomPassNodes.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Nodes/DiffusionProfileNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Nodes/EmissionNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Nodes/ExposureNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Nodes/FresnelEquationNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Nodes/HDSampleBufferNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Nodes/HDSceneColorNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Nodes/HDSceneDepthNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Nodes/RayTracingQualityNode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Nodes/SurfaceGradientResolveNormal.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/PassDescriptorExtension.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/ShaderGraphVersion.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Slots/DefaultMaterialSlot.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Slots/DiffusionProfileInputMaterialSlot.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/SubTargetPropertiesGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/SubTargetPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/SurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/SurfaceSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/TargetData/BuiltinData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/TargetData/HDTargetData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/TargetData/LightingData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/TargetData/SystemData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/ShaderGraph/Views/DiffusionProfileSlotControlView.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/SixWayLit/ShaderGraph/CreateSixWayShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/SixWayLit/ShaderGraph/HDSixWayData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/SixWayLit/ShaderGraph/HDSixWaySubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/SixWayLit/ShaderGraph/SixWaySurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/SixWayLit/SixWayGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/StackLit/ShaderGraph/CreateStackLitShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/StackLit/ShaderGraph/StackLitAdvancedOptionsPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/StackLit/ShaderGraph/StackLitData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/StackLit/ShaderGraph/StackLitSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/StackLit/ShaderGraph/StackLitSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/StackLit/ShaderGraph/StackLitSurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/TerrainLit/StandardsTerrainToHDTerrainLitUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/TerrainLit/TerrainLitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/AdvancedOptionsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/AxfMainSurfaceInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/AxfSurfaceInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/DecalSortingInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/DecalSurfaceInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/DecalSurfaceOptionsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/DetailInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/DistortionUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/EmissionUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/HDShaderGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/LayeringOptionsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/LayerListUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/LayersUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/LightingShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/LitShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/LitSurfaceInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/MaterialUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/MaterialUIBlockList.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/RefractionUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/ShaderGraphUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/SixWayUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/SurfaceOptionUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/TessellationOptionsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/TransparencyUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/UIBlocks/UnlitSurfaceInputsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Unlit/ShaderGraph/CreateHDUnlitShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Unlit/ShaderGraph/HDUnlitData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Unlit/ShaderGraph/HDUnlitDistortionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Unlit/ShaderGraph/HDUnlitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Unlit/ShaderGraph/HDUnlitSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Unlit/ShaderGraph/HDUnlitSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Unlit/ShaderGraph/HDUnlitSurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Unlit/ShaderGraph/UnlitShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Unlit/UnlitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Unlit/UnlitsToHDUnlitUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/CreateWaterShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/Node/BlendNormal_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/Node/ComputeVertexPosition_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/Node/EvaluateDisplacement_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/Node/EvaluateFoamData_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/Node/EvaluateRefractionData_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/Node/EvaluateScatteringColor_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/Node/EvaluateSimulationAdditionalData_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/Node/EvaluateSimulationCaustics_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/Node/EvaluateTipThickness_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/Node/GetCameraHeightFromWater.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/Node/PackVertexData_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/Node/UnpackData_Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/WaterData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/WaterDecalSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/WaterSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/WaterSubTarget.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Material/Water/ShaderGraph/WaterSurfaceOptionPropertyBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PackageInfo.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/BloomEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/ChromaticAberrationEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/ColorCurvesEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/CustomPostProcessVolumeComponentEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/DepthOfFieldEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/ExposureEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/FilmGrainEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/LiftGammaGainEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/MotionBlurEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/PaniniProjectionEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/ScreenSpaceLensFlareEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/SerializedGlobalPostProcessSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/ShadowsMidtonesHighlightsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/TonemappingEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/TrackballUIDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PostProcessing/VignetteEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PropertyDrawers/CustomPostProcessOrdersSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PropertyDrawers/CustomPostProcessVolumeComponentListPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PropertyDrawers/FrameSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PropertyDrawers/HDRPDefaultVolumeProfileSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PropertyDrawers/LookDevVolumeProfileSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/PropertyDrawers/RenderingPathFrameSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RayTracing/ReflectionKernelGenerator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RayTracing/SolidAngleKernelGenerator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/BaseUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDAdditionalCameraEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraEditor.Handlers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraPreview.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraUI.ContextualMenu.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraUI.Environment.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraUI.EnvironmentSkin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraUI.Output.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraUI.Output.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraUI.PhysicalCamera.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraUI.PhysicalCamera.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraUI.PresetInspector.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraUI.Rendering.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/HDCameraUI.Rendering.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Camera/SerializedHDCamera.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/CustomPass/CustomPassDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/CustomPass/CustomPassDrawerAttribute.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/CustomPass/CustomPassListSearchWindow.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/CustomPass/CustomPassVolumeEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/CustomPass/CustomPassVolumeGizmoDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/CustomPass/DrawRenderersCustomPassDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/CustomPass/FullScreenCustomPassDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/CustomPass/ObjectIDCustomPassDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/CustomPass/VrsCustomPassDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/HDAdditionalMeshRendererSettingsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/HDAdditionalMeshRendererSettingsUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/HDAssetFactory.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/HDEditorCLI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/HDEditorUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/HDLightUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/HDRenderPipelineEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/HDRenderPipelineMenuItems.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/HDRenderPipelineUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/HDRenderPipelineUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/HDShaderUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/IUpdateable.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/LineRendering/HDRenderPipeline.LineRendering.VolumeComponentEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/LineRendering/HDShaderPasses.LineRendering.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/PathTracing/PathTracingEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Raytracing/GlobalIlluminationEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Raytracing/LightClusterEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Raytracing/RayTracingSettingsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Raytracing/RayTracingShaderPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Raytracing/RecursiveRenderingEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Raytracing/SubSurfaceScatteringEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/ScalableSettingLevelParameterEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/DiffusionProfileSettingsListUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/FrameSettingsExtractedDatas.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/FrameSettingsUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/FrameSettingsUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/HDRenderingLayersLimitSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/QualitySettingsPanel.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/RenderPipelineSettingsUtilities.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedDynamicResolutionSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedFrameSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedGlobalDecalSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedGlobalLightLoopSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedGPUResidentDrawerSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedHDRenderPipelineAsset.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedHDShadowInitParameters.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedLightingQualitySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedLowResTransparencySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedPostProcessingQualitySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedRenderPipelineSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedScalableSetting.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedScalableSettingValue.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedVirtualTexturingSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/Settings/SerializedXRSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/TargetMidGrayParameterDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/VirtualTexturingSettingsUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipeline/VolumeComponentWithQualityEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/RenderPipelineResources/HDProjectSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/SceneTemplates/HDRPBasicScenePipeline.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Sky/AtmosphericScattering/FogEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Sky/CloudSystem/CloudLayer/CloudLayerEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Sky/GradientSky/GradientSkyEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Sky/HDLightingWindowEnvironmentSection.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Sky/HDRISky/HDRISkyEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Sky/PhysicallyBasedSky/PhysicallyBasedSkyEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Sky/SkySettingsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Sky/StaticLightingSkyEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Sky/VisualEnvironmentEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Tools/ColorCheckerToolEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Upgraders/UpgradeStandardShaderMaterials.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Outputs/VFXAbstractDistortionOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Outputs/VFXAbstractParticleHDRPLitOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Outputs/VFXAbstractParticleHDRPOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Outputs/VFXDecalHDRPOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Outputs/VFXDistortionMeshOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Outputs/VFXDistortionPlanarPrimitiveOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Outputs/VFXDistortionQuadStripOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Outputs/VFXLitCubeOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Outputs/VFXLitMeshOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Outputs/VFXLitPlanarPrimitiveOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Outputs/VFXLitQuadStripOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Outputs/VFXLitSphereOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Outputs/VFXVolumetricFogOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/UIBlocks/VFXShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/Utility/VFXHDRPSettingsUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/VFXHDRPBinder.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/VFXGraph/VFXHDRPSubOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterDecal/WaterDecalEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterDecal/WaterDecalShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterDecal/WaterDecalSurfaceOptionsUIBlock.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterExcluder/WaterExcluderEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterRenderingEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterSurface/VFX/SampleWaterSurface.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterSurface/WaterSurfaceEditor.Appearance.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterSurface/WaterSurfaceEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterSurface/WaterSurfaceEditor.Decal.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterSurface/WaterSurfaceEditor.Foam.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterSurface/WaterSurfaceEditor.Miscellaneous.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterSurface/WaterSurfaceEditor.Simulation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterSurface/WaterSurfaceEditor.Tooltips.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterSurfaceMenuItems.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Water/WaterSystemResourcesStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Wizard/HDWizard.Configuration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Wizard/HDWizard.ProjectSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Wizard/HDWizard.UIElement.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Wizard/HDWizard.UserSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Editor/Wizard/HDWizard.Window.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.UnityAdditionalFile.txt"
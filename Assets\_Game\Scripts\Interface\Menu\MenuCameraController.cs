using UnityEngine;
using System.Collections;

public class MenuCameraController : MonoBehaviour
{
    [System.Serializable]
    public class CameraPosition
    {
        public Vector3 position;
        public Vector3 rotation;
        public float transitionDuration = 2f;
        public AnimationCurve easingCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    }

    [Header("Camera Settings")]
    [SerializeField] private CameraPosition[] cameraPositions;
    [SerializeField] private float idleMovementAmount = 0.1f;
    [SerializeField] private float idleMovementSpeed = 1f;
    [SerializeField] private float idleRotationAmount = 2f;
    [SerializeField] private float idleRotationSpeed = 0.5f;
    
    [Header("Mouse Response Settings")]
    [SerializeField] private float mouseMovementSensitivity = 0.1f;
    [SerializeField] private float mouseRotationSensitivity = 0.5f;
    [SerializeField] private float mouseLerpSpeed = 5f;
    [SerializeField] private Vector2 mouseMovementLimit = new Vector2(0.5f, 0.3f);
    [SerializeField] private Vector2 mouseRotationLimit = new Vector2(5f, 5f);
    
    [Header("Transition Settings")]
    [SerializeField] private bool randomizePositions = true;
    [SerializeField] private float minTimeBetweenTransitions = 5f;
    [SerializeField] private float maxTimeBetweenTransitions = 10f;

    private int currentPositionIndex = 0;
    private Vector3 initialPosition;
    private Vector3 initialRotation;
    private float nextTransitionTime;
    private bool isTransitioning;

    // Mouse movement variables
    private Vector3 targetMouseOffset;
    private Vector3 currentMouseOffset;
    private Vector3 targetMouseRotation;
    private Vector3 currentMouseRotation;
    private Vector2 normalizedMousePos;

    private void Start()
    {
        if (cameraPositions == null || cameraPositions.Length == 0)
        {
            Debug.LogWarning("No camera positions defined. Adding current transform as default position.");
            cameraPositions = new CameraPosition[] 
            { 
                new CameraPosition 
                { 
                    position = transform.position,
                    rotation = transform.eulerAngles 
                } 
            };
        }

        initialPosition = transform.position;
        initialRotation = transform.eulerAngles;
        SetNextTransitionTime();
    }

    private void Update()
    {
        if (!isTransitioning)
        {
            // Update mouse input
            UpdateMouseInput();
            
            // Apply both idle and mouse-based movement
            ApplyCombinedMovement();

            // Check if it's time for next transition
            if (Time.time >= nextTransitionTime)
            {
                StartNextTransition();
            }
        }
    }

    private void UpdateMouseInput()
    {
        // Get normalized mouse position (-1 to 1 range)
        normalizedMousePos = new Vector2(
            (Input.mousePosition.x / Screen.width) * 2 - 1,
            (Input.mousePosition.y / Screen.height) * 2 - 1
        );

        // Calculate target position offset based on mouse position
        targetMouseOffset = new Vector3(
            normalizedMousePos.x * mouseMovementLimit.x * mouseMovementSensitivity,
            normalizedMousePos.y * mouseMovementLimit.y * mouseMovementSensitivity,
            0
        );

        // Calculate target rotation based on mouse position
        targetMouseRotation = new Vector3(
            -normalizedMousePos.y * mouseRotationLimit.x * mouseRotationSensitivity,
            normalizedMousePos.x * mouseRotationLimit.y * mouseRotationSensitivity,
            0
        );

        // Smoothly interpolate current offsets towards target
        currentMouseOffset = Vector3.Lerp(currentMouseOffset, targetMouseOffset, Time.deltaTime * mouseLerpSpeed);
        currentMouseRotation = Vector3.Lerp(currentMouseRotation, targetMouseRotation, Time.deltaTime * mouseLerpSpeed);
    }

    private void ApplyCombinedMovement()
    {
        // Calculate idle movement
        float xOffset = Mathf.Sin(Time.time * idleMovementSpeed) * idleMovementAmount;
        float yOffset = Mathf.Sin(Time.time * idleMovementSpeed * 0.85f) * idleMovementAmount;
        float zOffset = Mathf.Sin(Time.time * idleMovementSpeed * 1.1f) * idleMovementAmount;
        Vector3 idleOffset = new Vector3(xOffset, yOffset, zOffset);

        // Calculate idle rotation
        float rotXOffset = Mathf.Sin(Time.time * idleRotationSpeed) * idleRotationAmount;
        float rotYOffset = Mathf.Sin(Time.time * idleRotationSpeed * 0.7f) * idleRotationAmount;
        Vector3 idleRotation = new Vector3(rotXOffset, rotYOffset, 0);

        // Combine idle and mouse-based movement
        Vector3 finalPosition = initialPosition + idleOffset + currentMouseOffset;
        Vector3 finalRotation = initialRotation + idleRotation + currentMouseRotation;

        // Apply final position and rotation
        transform.position = finalPosition;
        transform.eulerAngles = finalRotation;
    }

    private void StartNextTransition()
    {
        if (cameraPositions.Length <= 1) return;

        int nextIndex;
        if (randomizePositions)
        {
            do
            {
                nextIndex = Random.Range(0, cameraPositions.Length);
            } while (nextIndex == currentPositionIndex);
        }
        else
        {
            nextIndex = (currentPositionIndex + 1) % cameraPositions.Length;
        }

        StartCoroutine(TransitionToPosition(nextIndex));
        currentPositionIndex = nextIndex;
    }

    private IEnumerator TransitionToPosition(int targetIndex)
    {
        isTransitioning = true;
        
        Vector3 startPos = transform.position;
        Vector3 startRot = transform.eulerAngles;
        Vector3 targetPos = cameraPositions[targetIndex].position;
        Vector3 targetRot = cameraPositions[targetIndex].rotation;
        float duration = cameraPositions[targetIndex].transitionDuration;
        AnimationCurve easingCurve = cameraPositions[targetIndex].easingCurve;

        float elapsed = 0;
        while (elapsed < duration)
        {
            float t = elapsed / duration;
            float curveValue = easingCurve.Evaluate(t);

            transform.position = Vector3.Lerp(startPos, targetPos, curveValue);
            transform.eulerAngles = Vector3.Lerp(startRot, targetRot, curveValue);

            elapsed += Time.deltaTime;
            yield return null;
        }

        // Reset mouse offsets after transition
        currentMouseOffset = Vector3.zero;
        currentMouseRotation = Vector3.zero;
        targetMouseOffset = Vector3.zero;
        targetMouseRotation = Vector3.zero;

        // Ensure we end up exactly at target
        transform.position = targetPos;
        transform.eulerAngles = targetRot;

        initialPosition = targetPos;
        initialRotation = targetRot;

        isTransitioning = false;
        SetNextTransitionTime();
    }

    private void SetNextTransitionTime()
    {
        nextTransitionTime = Time.time + Random.Range(minTimeBetweenTransitions, maxTimeBetweenTransitions);
    }

    // Optional: Public method to force a transition to a specific position
    public void TransitionToIndex(int index)
    {
        if (index >= 0 && index < cameraPositions.Length && !isTransitioning)
        {
            StartCoroutine(TransitionToPosition(index));
            currentPositionIndex = index;
        }
    }
}
{ "pid": 81620, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 81620, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 81620, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 81620, "tid": 1, "ts": 1755479396245234, "dur": 491975, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 81620, "tid": 1, "ts": 1755479396251859, "dur": 65684, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 81620, "tid": 1, "ts": 1755479396261645, "dur": 55240, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 81620, "tid": 1, "ts": 1755479396345725, "dur": 17893, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 81620, "tid": 1, "ts": 1755479396365144, "dur": 88535, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 81620, "tid": 1, "ts": 1755479396453738, "dur": 22560, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 81620, "tid": 1, "ts": 1755479396476321, "dur": 252351, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 81620, "tid": 1, "ts": 1755479396734697, "dur": 2356, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 81620, "tid": 1, "ts": 1755479396737211, "dur": 442, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 81620, "tid": 1, "ts": 1755479396748257, "dur": 2405, "ph": "X", "name": "", "args": {} },
{ "pid": 81620, "tid": 1, "ts": 1755479396747534, "dur": 3497, "ph": "X", "name": "Write chrome-trace events", "args": {} },

{ "pid": 34500, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 34500, "tid": 1, "ts": 1755479373117173, "dur": 8638, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 34500, "tid": 1, "ts": 1755479373125816, "dur": 143752, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 34500, "tid": 1, "ts": 1755479373269580, "dur": 3521, "ph": "X", "name": "Write<PERSON>son", "args": {} },
{ "pid": 34500, "tid": 16966, "ts": 1755479375148406, "dur": 1280, "ph": "X", "name": "", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479373114817, "dur": 16900, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479373131720, "dur": 2001906, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479373132885, "dur": 2483, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479373135375, "dur": 1616, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479373136994, "dur": 39, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479373137036, "dur": 828, "ph": "X", "name": "ProcessMessages 483", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479373137868, "dur": 7793, "ph": "X", "name": "ReadAsync 483", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479373145667, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479373145670, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479373145693, "dur": 660, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479373146357, "dur": 1963435, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479375109804, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479375109808, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479375109897, "dur": 2555, "ph": "X", "name": "ProcessMessages 15973", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479375112457, "dur": 5410, "ph": "X", "name": "ReadAsync 15973", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479375117875, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479375117879, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479375117924, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479375117927, "dur": 317, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479375118248, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479375118279, "dur": 374, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 34500, "tid": 12884901888, "ts": 1755479375118656, "dur": 14258, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 34500, "tid": 16966, "ts": 1755479375149690, "dur": 36, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 34500, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 34500, "tid": 8589934592, "ts": 1755479373111523, "dur": 161645, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 34500, "tid": 8589934592, "ts": 1755479373273171, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 34500, "tid": 8589934592, "ts": 1755479373273175, "dur": 1243, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 34500, "tid": 16966, "ts": 1755479375149728, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 34500, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 34500, "tid": 4294967296, "ts": 1755479373084256, "dur": 2051425, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 34500, "tid": 4294967296, "ts": 1755479373089558, "dur": 14205, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 34500, "tid": 4294967296, "ts": 1755479375135743, "dur": 5824, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 34500, "tid": 4294967296, "ts": 1755479375138923, "dur": 33, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 34500, "tid": 4294967296, "ts": 1755479375142038, "dur": 20, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 34500, "tid": 16966, "ts": 1755479375149736, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1755479373130601, "dur":5225, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479373135837, "dur":218, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479373136093, "dur":611, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479373136767, "dur":318, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479373137086, "dur":1982088, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479375119176, "dur":205, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479375119576, "dur":4007, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1755479373136788, "dur":305, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755479373137629, "dur":1326, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1755479373137106, "dur":8842, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1755479373147777, "dur":1964621, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1755479373145978, "dur":1973194, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755479373146010, "dur":1973176, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479373136814, "dur":288, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755479373137126, "dur":1982056, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479373136841, "dur":267, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479373137108, "dur":138649, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479373285722, "dur":454, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":5, "ts":1755479373275758, "dur":10423, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755479373286182, "dur":1833012, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479373136929, "dur":199, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755479373137128, "dur":1982056, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479373136952, "dur":175, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755479373137128, "dur":1982064, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755479373146038, "dur":1973160, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755479373146061, "dur":1973127, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479373136866, "dur":248, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479373137115, "dur":149071, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755479373286187, "dur":1832989, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479373136902, "dur":219, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755479373137121, "dur":1982052, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479373136974, "dur":152, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755479373137126, "dur":1982051, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755479375131782, "dur":681, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 34500, "tid": 16966, "ts": 1755479375150384, "dur": 4041, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 34500, "tid": 16966, "ts": 1755479375154586, "dur": 2685, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 34500, "tid": 16966, "ts": 1755479375147535, "dur": 10826, "ph": "X", "name": "Write chrome-trace events", "args": {} },

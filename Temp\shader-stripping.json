{"totalVariantsIn": 370657, "totalVariantsOut": 4158, "shaders": [{"inputVariants": 6, "outputVariants": 6, "name": "Shader Graphs/PhysicallyBasedSky", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "PBR Sky cubemap (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 14.991900000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBR Sky cubemap (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.12340000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "PBR Sky (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.065}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBR Sky (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1041}]}]}, {"inputVariants": 434, "outputVariants": 122, "name": "Shader Graphs/Water", "pipelines": [{"inputVariants": 434, "outputVariants": 122, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.3208}, {"inputVariants": 72, "outputVariants": 12, "variantName": "WaterGBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.8437}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5145000000000001}, {"inputVariants": 72, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.9642000000000001}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Hull)", "stripTimeMs": 0.4335}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Domain)", "stripTimeMs": 0.4086}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0558}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMask (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5071}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMask (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5312}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMaskLowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6626000000000001}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMaskLowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.7518}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Shader Graphs/Water Decal", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7624000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08900000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0823}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0917}]}]}, {"inputVariants": 37, "outputVariants": 20, "name": "Shader Graphs/SolidColor", "pipelines": [{"inputVariants": 37, "outputVariants": 20, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.9012}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0978}, {"inputVariants": 2, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0918}, {"inputVariants": 4, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.14400000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09050000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.18510000000000001}, {"inputVariants": 4, "outputVariants": 2, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.108}, {"inputVariants": 4, "outputVariants": 2, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1056}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.016}, {"inputVariants": 2, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1374}, {"inputVariants": 2, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.2214}, {"inputVariants": 2, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.11860000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1272}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.12090000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1265}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "HDRP/DefaultFogVolume", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.9298000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.12810000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07780000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.11230000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.055}, {"inputVariants": 2, "outputVariants": 2, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0854}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Shader Graphs/Sample Water Decal", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.2614}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0848}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08360000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0896}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Skybox/Cubemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0376}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0335}]}]}, {"inputVariants": 144, "outputVariants": 144, "name": "Hidden/HDRP/Sky/CloudLayer", "pipelines": [{"inputVariants": 144, "outputVariants": 144, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.0711}, {"inputVariants": 24, "outputVariants": 24, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3584}, {"inputVariants": 48, "outputVariants": 48, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7571}, {"inputVariants": 48, "outputVariants": 48, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.6986}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/PostProcessing/Debug/Waveform", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019200000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/DLSSBiasColorMask", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6151}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0461}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/WaterCaustics", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5722}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.036000000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ApplyDistortion", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5202}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0354}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/CameraMotionVectors", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.455}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0364}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CompositeUI", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4974}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1487}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/VoxelizeShader", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4419}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0317}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0325}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0309}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "Hidden/HDRP/DebugExposure", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.009300000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0091}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0088}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0082}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/HDRP/GGXConvolve", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5021}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0551}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/preIntegratedFGD_GGXDisneyDiffuse", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4697}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030500000000000003}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/CoreSRP/CoreCopy", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4349}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0472}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyMS (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031}, {"inputVariants": 2, "outputVariants": 2, "variantName": "CopyMS (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0426}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/UpsampleTransparent", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4934}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027800000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0533}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028900000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.048}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/Sky/GradientSky", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.48660000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029400000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0264}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/CombineLighting", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.43560000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0291}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027600000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0334}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/ColorPyramidPS", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.47640000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030100000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_<PERSON>ner", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.46290000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0318}]}]}, {"inputVariants": 44, "outputVariants": 24, "name": "Hidden/HDRP/OpaqueAtmosphericScattering", "pipelines": [{"inputVariants": 44, "outputVariants": 24, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5155000000000001}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0824}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029400000000000003}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MSAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06670000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027600000000000003}, {"inputVariants": 16, "outputVariants": 8, "variantName": "Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.21530000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA + Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0268}, {"inputVariants": 16, "outputVariants": 8, "variantName": "MSAA + Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1615}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ClearBlack", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5399}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0488}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VFX/Empty", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.42200000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0413}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.43710000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0431}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/MaterialLoading", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.48140000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0442}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/ProbeVolumeFragmentationDebug", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.8069000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0342}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/Core/ProbeVolumeDebug", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.00030000000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/preIntegratedFGD_CharlieFabricLambert", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.48460000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0291}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CopyStencilBuffer", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 - Copy stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4632}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 - Copy stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0364}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 - Write 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 - Write 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 - Export HTILE for stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0322}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 - Export HTILE for stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 - Initialize Stencil UAV copy with 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0284}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 - Initialize Stencil UAV copy with 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 4 - Update Stencil UAV copy with Stencil Ref (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 4 - Update Stencil UAV copy with Stencil Ref (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0268}]}]}, {"inputVariants": 1284, "outputVariants": 1284, "name": "Hidden/HDRP/TemporalAA", "pipelines": [{"inputVariants": 1284, "outputVariants": 1284, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "TAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4726}, {"inputVariants": 320, "outputVariants": 320, "variantName": "TAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 4.721900000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Excluded From TAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0596}, {"inputVariants": 320, "outputVariants": 320, "variantName": "Excluded From TAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 5.1083}, {"inputVariants": 1, "outputVariants": 1, "variantName": "TAAU (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0531}, {"inputVariants": 320, "outputVariants": 320, "variantName": "TAAU (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 4.6532}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy History (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053200000000000004}, {"inputVariants": 320, "outputVariants": 320, "variantName": "Copy History (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 4.472300000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/CopyDepthBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy Depth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4913}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy Depth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0431}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugViewMaterialGBuffer", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0671}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/XROcclusionMesh", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5592}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.05}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/IntegrateHDRI", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6348}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0317}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/ChromaKeying", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ChromaKeying (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.49760000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ChromaKeying (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0296}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Renderers/Thickness", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessOpaque (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6446000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessOpaque (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0424}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessTransparent (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0408}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessTransparent (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0409}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/AlphaInjection", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "AlphaInjection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6877}, {"inputVariants": 1, "outputVariants": 1, "variantName": "AlphaInjection (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/DebugVTBlit", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0099}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0078000000000000005}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/CharlieConvolve", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.008700000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/CubeToPano", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4318}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0302}]}]}, {"inputVariants": 769, "outputVariants": 257, "name": "Hidden/HDRP/FinalPass", "pipelines": [{"inputVariants": 769, "outputVariants": 257, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4203}, {"inputVariants": 768, "outputVariants": 256, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 12.6042}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/VrsVisualization", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "VrsVisualization (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5436}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VrsVisualization (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0439}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Outline", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5292}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.039900000000000005}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/HDRP/DownsampleDepth", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5578000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.058600000000000006}]}]}, {"inputVariants": 288790, "outputVariants": 810, "name": "HDRP/Lit", "pipelines": [{"inputVariants": 288790, "outputVariants": 810, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 120, "outputVariants": 48, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 2.4997000000000003}, {"inputVariants": 5184, "outputVariants": 240, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 51.1539}, {"inputVariants": 12, "outputVariants": 12, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.27390000000000003}, {"inputVariants": 12, "outputVariants": 12, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.24050000000000002}, {"inputVariants": 144, "outputVariants": 48, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 2.4408000000000003}, {"inputVariants": 360, "outputVariants": 90, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 4.843100000000001}, {"inputVariants": 72, "outputVariants": 24, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.2372}, {"inputVariants": 144, "outputVariants": 36, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.9759}, {"inputVariants": 36, "outputVariants": 6, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3932}, {"inputVariants": 36, "outputVariants": 6, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.29600000000000004}, {"inputVariants": 144, "outputVariants": 12, "variantName": "TransparentBackface (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.1234}, {"inputVariants": 93312, "outputVariants": 36, "variantName": "TransparentBackface (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 584.5858000000001}, {"inputVariants": 144, "outputVariants": 72, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 2.3760000000000003}, {"inputVariants": 186624, "outputVariants": 156, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1439.848}, {"inputVariants": 18, "outputVariants": 6, "variantName": "TransparentDepthPostpass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2664}, {"inputVariants": 18, "outputVariants": 6, "variantName": "TransparentDepthPostpass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2204}, {"inputVariants": 4, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0268}, {"inputVariants": 12, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0631}, {"inputVariants": 864, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 8.6394}, {"inputVariants": 432, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 4.567}, {"inputVariants": 864, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 8.654300000000001}, {"inputVariants": 12, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.5904}, {"inputVariants": 72, "outputVariants": 0, "variantName": "SubSurfaceDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.1313}, {"inputVariants": 6, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.2659}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.4476}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/DepthValues", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 2.0819}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0792}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0366}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.058800000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029400000000000003}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.026600000000000002}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0519}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/MaterialError", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5154}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.046}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/PostProcessing/Debug/Vectorscope", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0094}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CustomPassUtils", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6282}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0307}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027800000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Downsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0287}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Downsample (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0281}, {"inputVariants": 1, "outputVariants": 1, "variantName": "HorizontalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "HorizontalBlur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0356}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VerticalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0317}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VerticalBlur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugColorPicker", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0092}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugLocalVolumetricFogAtlas", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0098}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0091}]}]}, {"inputVariants": 26, "outputVariants": 26, "name": "Hidden/HDRP/LensFlareDataDriven", "pipelines": [{"inputVariants": 26, "outputVariants": 26, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareAdditive (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7435}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareAdditive (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0771}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareScreen (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0473}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareScreen (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0805}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlarePremultiply (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0461}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlarePremultiply (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0855}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareLerp (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0531}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareLerp (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0711}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0284}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0251}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/FallbackError", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.8104}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0478}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/HDRP/CompositeLines", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeAll (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6797000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeAll (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeColorOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeColorOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027800000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeDepthMovecOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.044500000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeDepthMovecOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0307}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugLightVolumes", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0086}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0074}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ClearStencilBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7535000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028300000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/bypassShader", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "bypassShader (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5561}, {"inputVariants": 1, "outputVariants": 1, "variantName": "bypassShader (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0337}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/ScriptableRenderPipeline/DebugDisplayHDShadowMap", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "RegularShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6172000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RegularShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0328}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VarianceShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0334}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VarianceShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0359}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/PostProcessing/SubpixelMorphologicalAntialiasing", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Edge detection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6029}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Edge detection (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0733}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blend Weights Calculation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.030600000000000002}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Blend Weights Calculation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0644}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Neighborhood Blending (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028200000000000003}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Neighborhood Blending (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06470000000000001}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/LensFlareScreenSpace", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpac Prefilter (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6088}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpac Prefilter (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Downsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Downsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Upsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0279}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Upsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Composition (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0252}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Composition (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Write to BloomTexture (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0268}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Write to BloomTexture (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.025500000000000002}]}]}, {"inputVariants": 3, "outputVariants": 0, "name": "Hidden/HDRP/DebugHDR", "pipelines": [{"inputVariants": 3, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0094}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0081}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0074}]}]}, {"inputVariants": 868, "outputVariants": 52, "name": "Hidden/HDRP/Sky/HDRISky", "pipelines": [{"inputVariants": 868, "outputVariants": 52, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FragBaking (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5865}, {"inputVariants": 216, "outputVariants": 12, "variantName": "FragBaking (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.6011}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRender (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029}, {"inputVariants": 216, "outputVariants": 12, "variantName": "FragRender (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.7102000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRenderBackplate (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029300000000000003}, {"inputVariants": 216, "outputVariants": 12, "variantName": "FragRenderBackplate (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.7402000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRenderBackplateDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.030100000000000002}, {"inputVariants": 216, "outputVariants": 12, "variantName": "FragRenderBackplateDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.7513}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/CustomClear", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ClearColorAndStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6193000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ClearColorAndStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.046200000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawTextureAndClearStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.040600000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawTextureAndClearStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041600000000000005}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/HDRP/Sky/PbrSky", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "PBRSky Cubemap (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6141}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBRSky Cubemap (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0528}, {"inputVariants": 1, "outputVariants": 1, "variantName": "PBRSky (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0323}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBRSky (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0451}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/MotionPostProcess", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "MotionPostProcess (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.63}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MotionPostProcess (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_CookTorrance", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6688000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032600000000000004}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/BlitColorAndDepth", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ColorOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6968000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0854}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorAndDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06860000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorAndDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08660000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/DebugOccluder", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DebugOccluder (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5974}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DebugOccluder (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0371}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/WaterExclusion", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "StencilTag (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6651}, {"inputVariants": 2, "outputVariants": 2, "variantName": "StencilTag (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.049100000000000005}]}]}, {"inputVariants": 4, "outputVariants": 3, "name": "Hidden/Core/ProbeVolumeSamplingDebug", "pipelines": [{"inputVariants": 4, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6685}, {"inputVariants": 3, "outputVariants": 2, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.081}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/ScreenSpaceShadows", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0152}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugDisplayLatlong", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.008}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/CoreResources/FilterAreaLightCookies", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6147}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0337}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0304}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0296}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.030600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029400000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029500000000000002}]}]}, {"inputVariants": 86, "outputVariants": 48, "name": "HDRP/Unlit", "pipelines": [{"inputVariants": 86, "outputVariants": 48, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6873}, {"inputVariants": 12, "outputVariants": 12, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1806}, {"inputVariants": 3, "outputVariants": 3, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0708}, {"inputVariants": 12, "outputVariants": 12, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.21530000000000002}, {"inputVariants": 6, "outputVariants": 3, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09090000000000001}, {"inputVariants": 12, "outputVariants": 6, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1464}, {"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06520000000000001}, {"inputVariants": 6, "outputVariants": 6, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0945}, {"inputVariants": 3, "outputVariants": 0, "variantName": "DistortionVectors (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.030500000000000003}, {"inputVariants": 6, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.032}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1044}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.13140000000000002}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0888}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1971}, {"inputVariants": 2, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1012}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1188}]}]}, {"inputVariants": 16, "outputVariants": 0, "name": "Hidden/HDRP/DebugViewTiles", "pipelines": [{"inputVariants": 16, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 16, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061500000000000006}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ScriptableRenderPipeline/ShadowBlit", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "BlitShadows (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6757000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BlitShadows (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/CustomPassRenderersUtils", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "DepthToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6903}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0429}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041800000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.046}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NormalToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049300000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NormalToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.046}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TangentToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046900000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TangentToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.049800000000000004}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugBlitQuad", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.009600000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0086}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/HDRP/WaterDecal", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DeformationDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5405}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DeformationDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0315}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.030500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0302}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MaskDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MaskDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0318}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LargeCurrentDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0309}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LargeCurrentDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0286}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RipplesCurrentDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RipplesCurrentDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0315}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamAttenuation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamAttenuation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026600000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCubemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.562}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0318}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_Ward", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4441}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0324}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/DebugOcclusionTest", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4213}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0308}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/SRP/BlitCubeTextureFace", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.46590000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0325}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader Graph/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5069}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0322}]}]}, {"inputVariants": 4, "outputVariants": 3, "name": "Hidden/Core/ProbeVolumeOffsetDebug", "pipelines": [{"inputVariants": 4, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5718}, {"inputVariants": 3, "outputVariants": 2, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0596}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/ColorResolve", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA1X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5227}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA1X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA2X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0274}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA2X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0309}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA4X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.025400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA4X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA8X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0245}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA8X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0264}]}]}, {"inputVariants": 200, "outputVariants": 200, "name": "Hidden/HDRP/Blit", "pipelines": [{"inputVariants": 200, "outputVariants": 200, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Nearest (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6295000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Nearest (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0656}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bilinear (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0833}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bilinear (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0645}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0635}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuad (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0825}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06470000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuad (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0669}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0711}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0611}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0699}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061500000000000006}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0613}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0659}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0692}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061200000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06380000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061700000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0699}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1907}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07590000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0862}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06430000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07590000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0815}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061700000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0683}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0641}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0658}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0688}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0694}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralLuminance (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.062}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0687}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralAlpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0723}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0795}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralRed (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.062400000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.062}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadLuminance (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0649}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0717}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadAlpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0661}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.062200000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadRed (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07640000000000001}, {"inputVariants": 8, "outputVariants": 8, "variantName": "NearestCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11410000000000001}, {"inputVariants": 8, "outputVariants": 8, "variantName": "NearestCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.14600000000000002}, {"inputVariants": 8, "outputVariants": 8, "variantName": "BilinearCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1174}, {"inputVariants": 8, "outputVariants": 8, "variantName": "BilinearCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1456}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ScriptableRenderPipeline/ShadowClear", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ClearShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7661}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ClearShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0292}]}]}, {"inputVariants": 5, "outputVariants": 3, "name": "Hidden/HDRP/XRMirrorView", "pipelines": [{"inputVariants": 5, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5896}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0881}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugFullScreen", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0088}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/MotionVecResolve", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5318}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0313}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029300000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0315}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0262}]}]}, {"inputVariants": 3, "outputVariants": 2, "name": "Hidden/HDRP/Material/Decal/DecalNormalBuffer", "pipelines": [{"inputVariants": 3, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5930000000000001}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.043300000000000005}]}]}, {"inputVariants": 73, "outputVariants": 34, "name": "Shader Graphs/TIPS_Mesh 2", "pipelines": [{"inputVariants": 73, "outputVariants": 34, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6221}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1217}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1254}, {"inputVariants": 8, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.16870000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1177}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2412}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2071}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2059}, {"inputVariants": 4, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.2338}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.4203}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.17830000000000001}, {"inputVariants": 4, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.3501}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1139}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.25720000000000004}]}]}, {"inputVariants": 59, "outputVariants": 24, "name": "Shader Graphs/TIPS_Mesh", "pipelines": [{"inputVariants": 59, "outputVariants": 24, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5760000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0717}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0974}, {"inputVariants": 8, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1495}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07680000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.17350000000000002}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.18680000000000002}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.19310000000000002}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019200000000000002}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.2368}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.33990000000000004}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.18480000000000002}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1328}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0935}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.20040000000000002}]}]}, {"inputVariants": 22211, "outputVariants": 90, "name": "Samples/SamplesLit_Inter", "pipelines": [{"inputVariants": 22211, "outputVariants": 90, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.0772000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.17650000000000002}, {"inputVariants": 8, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3547}, {"inputVariants": 32, "outputVariants": 6, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.6553}, {"inputVariants": 4, "outputVariants": 2, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.10450000000000001}, {"inputVariants": 4, "outputVariants": 2, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1}, {"inputVariants": 4, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0204}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3603}, {"inputVariants": 48, "outputVariants": 12, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.1736}, {"inputVariants": 16, "outputVariants": 4, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6033000000000001}, {"inputVariants": 576, "outputVariants": 16, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 7.679200000000001}, {"inputVariants": 16, "outputVariants": 8, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5275000000000001}, {"inputVariants": 20736, "outputVariants": 20, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 137.99280000000002}, {"inputVariants": 2, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 4.0132}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.5405}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.8385}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 3.7866}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1173}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.9076000000000002}]}, {"inputVariants": 0, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Forward (ForwardBase) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0004}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn ForwardAdd (ForwardAdd) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.00030000000000000003}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Deferred (Deferred) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Unlit/Texture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0618}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0472}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "FullScreen/test", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6625}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Distance Field", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.66}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0721}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/EdgePicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Edges (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.8336}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Edges (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0324}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "FullScreen/TIPS", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Compositing (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6108}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Compositing (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0279}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/VertexPicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Vertices (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6635}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Vertices (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0328}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/FacePicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Base (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5879}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Base (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027800000000000002}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Sprite", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.609}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0664}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/HideVertices", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6305000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028300000000000002}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "TextMeshPro/Mobile/Distance Field", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7865000000000001}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1983}]}]}, {"inputVariants": 32887, "outputVariants": 138, "name": "Shader Graphs/BakeryVolumeGraph", "pipelines": [{"inputVariants": 32887, "outputVariants": 138, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 3.0568}, {"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1534}, {"inputVariants": 6, "outputVariants": 6, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.35250000000000004}, {"inputVariants": 48, "outputVariants": 18, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.6484}, {"inputVariants": 3, "outputVariants": 0, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0424}, {"inputVariants": 3, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.026500000000000003}, {"inputVariants": 6, "outputVariants": 6, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.32370000000000004}, {"inputVariants": 72, "outputVariants": 18, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.7639}, {"inputVariants": 12, "outputVariants": 6, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.37520000000000003}, {"inputVariants": 864, "outputVariants": 48, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 10.158100000000001}, {"inputVariants": 12, "outputVariants": 6, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6217}, {"inputVariants": 31104, "outputVariants": 24, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 204.89370000000002}, {"inputVariants": 2, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0328}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 3.9372000000000003}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.49300000000000005}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.8728}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 3.7783}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1149}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 2.0415}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "HDRPSamples/LocalClouds", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.3114000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07350000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0748}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0679}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0699}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0692}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "CustomPass_SG/Outline", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DrawProcedural (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 2.7406}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawProcedural (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0724}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0627}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0655}]}]}, {"inputVariants": 59, "outputVariants": 24, "name": "Shader Graphs/Glitch_SG", "pipelines": [{"inputVariants": 59, "outputVariants": 24, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.1194}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.17070000000000002}, {"inputVariants": 8, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1957}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.081}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1675}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.18460000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.22340000000000002}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0173}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.23270000000000002}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.3936}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.2281}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.18810000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1221}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.24880000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Legacy Shaders/Particles/Alpha Blended", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0603}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0477}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/BillboardTree", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.034300000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029900000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Soft Occlusion Bark Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0339}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0286}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Soft Occlusion Leaves Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031900000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028900000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/CameraFacingBillboardTree", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0302}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0302}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Creator Albedo Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.030500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0317}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Creator Normal Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0304}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029500000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/TerrainEngine/Splatmap/Standard-BaseGen", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0316}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0298}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031100000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.033600000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0317}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/TerrainEngine/PaintHeight", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Raise/Lower Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0318}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Raise/Lower Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027600000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Stamp Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0269}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Stamp Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0317}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0309}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Smooth Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Smooth Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027800000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Texture (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0274}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Texture (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Holes (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0316}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Holes (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0308}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/HeightBlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0285}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0304}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/GenerateNormalmap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0286}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027700000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/TerrainEngine/TerrainLayerUtils", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Get Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0315}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Get Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0307}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0297}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0274}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit Copy Highest Mip (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0323}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit Copy Highest Mip (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027200000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/CrossBlendNeighbors", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0323}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0302}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TextCore/Sprite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029900000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029500000000000002}]}]}, {"inputVariants": 21844, "outputVariants": 64, "name": "ProBuilder6/Standard Vertex Color", "pipelines": [{"inputVariants": 21844, "outputVariants": 64, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.0347}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09870000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.22210000000000002}, {"inputVariants": 16, "outputVariants": 6, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5456}, {"inputVariants": 2, "outputVariants": 0, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0193}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0146}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2747}, {"inputVariants": 24, "outputVariants": 6, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.559}, {"inputVariants": 8, "outputVariants": 4, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.17650000000000002}, {"inputVariants": 288, "outputVariants": 16, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.3819000000000004}, {"inputVariants": 8, "outputVariants": 4, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3186}, {"inputVariants": 20736, "outputVariants": 16, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 133.3235}, {"inputVariants": 1, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0171}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 3.6928}, {"inputVariants": 2, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.2695}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.6364000000000001}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 3.5547}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.11710000000000001}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.8008000000000002}]}, {"inputVariants": 0, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Forward (ForwardBase) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.00030000000000000003}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn ForwardAdd (ForwardAdd) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0004}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Deferred (Deferred) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.00030000000000000003}, {"inputVariants": 0, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Graphy/Graph Mobile", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7931}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.057800000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Graphy/Graph Standard", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5605}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0528}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Legacy Shaders/VertexLit", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0985}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07010000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0347}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0375}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.054200000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0471}]}]}, {"inputVariants": 46, "outputVariants": 46, "name": "Legacy Shaders/Diffuse", "pipelines": [{"inputVariants": 46, "outputVariants": 46, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.20420000000000002}, {"inputVariants": 8, "outputVariants": 8, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.14550000000000002}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.111}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0818}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1621}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.15660000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-StencilWrite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0354}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-DepthNormalsTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0391}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0369}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0359}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0337}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0335}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.0329}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0328}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.0332}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.029900000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.030600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0275}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.030500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.029500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.030000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.029500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.0316}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0296}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.030100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.0302}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "Hidden/Internal-ScreenSpaceShadows", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08900000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08020000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.07740000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0833}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0881}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.08410000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0888}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.0741}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CombineDepthNormals", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0368}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0318}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.034}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0304}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.033600000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.033600000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ConvertTexture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0316}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029}]}]}, {"inputVariants": 54, "outputVariants": 54, "name": "Hidden/Internal-DeferredShading", "pipelines": [{"inputVariants": 54, "outputVariants": 54, "pipeline": "", "variants": [{"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.45430000000000004}, {"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3922}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.047900000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0362}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DeferredReflections", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.040100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0371}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049600000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.047}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-MotionVectors", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027100000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027600000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0296}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Flare", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0356}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.033100000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Halo", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0325}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyWithDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0286}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0261}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth_MSAA", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0284}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031200000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyHDRTonemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0328}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0309}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DebugPattern", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0316}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0308}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0262}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClip", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0269}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.028900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0333}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClipText", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0315}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0307}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0304}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0323}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.031200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.031}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureBlit", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0262}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.030100000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRect", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0302}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0303}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0324}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.031}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRectWithColorPerBorder", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0317}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0268}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0297}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-UIRDefault", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0485}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041100000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRAtlasBlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027800000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRDefaultWorld", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0261}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08650000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0907}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Mask", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06760000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06520000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0707}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06380000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/DefaultETC1", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0645}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.083}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0304}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0261}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0258}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0316}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0335}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0297}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0273}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlend", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0287}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0252}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VR/BlitTexArraySlice", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0324}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-ODSWorldTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0333}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031900000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0307}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0285}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.029500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0753}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0318}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.0291}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0302}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0285}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0281}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.027600000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.0273}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.0274}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0279}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0268}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.024800000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CubemapToEquirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.035500000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0308}]}]}, {"inputVariants": 5, "outputVariants": 5, "name": "Hidden/VR/BlitFromTex2DToTexArraySlice", "pipelines": [{"inputVariants": 5, "outputVariants": 5, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0323}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029900000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.028300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0369}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Geometry)", "stripTimeMs": 0.0327}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VideoComposite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0304}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031400000000000004}]}]}, {"inputVariants": 30, "outputVariants": 30, "name": "Hidden/VideoDecode", "pipelines": [{"inputVariants": 30, "outputVariants": 30, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0466}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0468}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0448}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.043000000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0476}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.042800000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0466}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0412}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0432}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0516}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0455}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0415}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0395}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.042100000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Compositing", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031400000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TextCore/Distance Field SSD", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0339}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0313}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/bypassShader", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "bypassShader (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "bypassShader (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Outline", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0304}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0256}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/MotionPostProcess", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "MotionPostProcess (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0313}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MotionPostProcess (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0296}]}]}]}
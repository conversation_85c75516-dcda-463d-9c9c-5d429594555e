using UnityEngine;
using UnityEngine.UIElements;
using System;
using System.Collections.Generic;
using AudioSystem;

public abstract class BaseMenuManager : MonoBehaviour
{
    [SerializeField] protected UIDocument uiDocument;
    protected VisualElement root;
    protected VisualElement menuContainer;
    protected Label titleLabel;
    protected List<Button> menuButtons = new List<Button>();
    protected List<Button> settingsButtons = new List<Button>();
    protected int currentSelectedIndex = -1;
    protected bool isUsingKeyboard = false;
    protected bool isInSubmenu = false;
    protected Dictionary<Button, Action> buttonActions = new Dictionary<Button, Action>();
    protected CustomCursor customCursor;

    // Audio
    [SerializeField] private AudioEventChannel uiAudioChannel;
    [SerializeField] private AudioEventDefinition buttonClickEvent;

    private VisualElement settingsRoot;
    protected VisualElement settingsContainer;
    protected VisualElement gameSettingsPanel;
    protected VisualElement graphicsSettingsPanel;
    protected VisualElement audioSettingsPanel;
    protected VisualElement controlsSettingsPanel;

    // Video settings UI elements
    protected Slider fovSlider;
    protected Label fovValueLabel;
    protected DropdownField fpsLimitDropdown;
    protected DropdownField backgroundFpsDropdown;

    #region Unity Lifecycle

    protected virtual void Awake()
    {
        InitializeUIElements();
        SetupCursor();
    }

    protected virtual void Start()
    {
        InitializeSettings();
        InitializeButtons();

        // Ensure current settings are applied whenever a menu is spawned
        if (SettingsManager.Instance != null)
        {
            SettingsManager.Instance.ApplyAllSettings();
        }
    }

    protected virtual void Update()
    {
        HandleKeyboardNavigation();
    }

    protected virtual void OnDestroy()
    {
        CleanupButtons();
        CleanupSettingsEvents();
    }

    protected virtual void CleanupSettingsEvents()
    {
        if (SettingsVideoManager.Instance != null)
        {
            SettingsVideoManager.Instance.OnFPSLimitChanged -= OnFPSLimitChanged;
            SettingsVideoManager.Instance.OnBackgroundFPSLimitChanged -= OnBackgroundFPSLimitChanged;
            SettingsVideoManager.Instance.OnFOVChanged -= OnFOVChanged;
        }

        // Clean up back buttons
        if (graphicsSettingsPanel != null)
        {
            var backButton = graphicsSettingsPanel.Q<Button>("back-to-settings-button");
            if (backButton != null)
            {
                backButton.clicked -= ShowSettingsMenu;
            }
        }

        if (audioSettingsPanel != null)
        {
            var backButton = audioSettingsPanel.Q<Button>("back-audio-settings-button");
            if (backButton != null)
            {
                backButton.clicked -= ShowSettingsMenu;
            }
        }

        if (gameSettingsPanel != null)
        {
            var backButton = gameSettingsPanel.Q<Button>("back-game-settings-button");
            if (backButton != null)
            {
                backButton.clicked -= ShowSettingsMenu;
            }
        }

        if (controlsSettingsPanel != null)
        {
            var backButton = controlsSettingsPanel.Q<Button>("back-controls-settings-button");
            if (backButton != null)
            {
                backButton.clicked -= ShowSettingsMenu;
            }
        }
    }

    #endregion

    #region Initialization

    protected virtual void InitializeUIElements()
    {
        if (uiDocument == null)
        {
            Debug.LogError($"UIDocument is not assigned to {GetType().Name}");
            enabled = false;
            return;
        }

        root = uiDocument.rootVisualElement;
        if (root == null)
        {
            Debug.LogError("Root VisualElement is null");
            enabled = false;
            return;
        }

        menuContainer = root.Q<VisualElement>("menu-container");
        titleLabel = root.Q<Label>("title");
    }

    protected virtual void InitializeSettings()
    {
        // Load settings template from Resources
        var settingsTemplate = Resources.Load<VisualTreeAsset>("SettingsSubMenu/SettingsMenu");
        if (settingsTemplate != null && menuContainer != null)
        {
            settingsRoot = settingsTemplate.Instantiate();
            settingsRoot.style.display = DisplayStyle.None;
            menuContainer.Add(settingsRoot);

            // Find all settings elements
            settingsContainer = settingsRoot.Q<VisualElement>("settings-container");
            gameSettingsPanel = settingsRoot.Q<VisualElement>("game-settings");
            graphicsSettingsPanel = settingsRoot.Q<VisualElement>("video-settings");
            audioSettingsPanel = settingsRoot.Q<VisualElement>("audio-settings");
            controlsSettingsPanel = settingsRoot.Q<VisualElement>("controls-settings");

            // Log panel references for debugging
            Debug.Log($"Settings panels found: Game={gameSettingsPanel != null}, Video={graphicsSettingsPanel != null}, Audio={audioSettingsPanel != null}, Controls={controlsSettingsPanel != null}");

            // Initialize settings UI elements
            InitializeVideoSettings();
            InitializeAudioSettings();
            InitializeGameSettings();
            InitializeControlsSettings();

            // Initialize settings buttons
            InitializeSettingsButton("game-settings-button", OpenGameplaySettings);
            InitializeSettingsButton("video-settings-button", OpenGraphicsSettings);
            InitializeSettingsButton("audio-settings-button", OpenAudioSettings);
            InitializeSettingsButton("controls-settings-button", OpenControlSettings);
            InitializeSettingsButton("back-settings-button", ShowStartMenu);
        }
        else
        {
            Debug.LogError("Failed to load SettingsMenu from Resources");
        }
    }

    protected virtual void InitializeGameSettings()
    {
        if (gameSettingsPanel != null)
        {
            // Add back button to game settings panel
            var existingBackButton = gameSettingsPanel.Q<Button>("back-game-settings-button");
            if (existingBackButton != null)
            {
                existingBackButton.clicked += ShowSettingsMenu;
                SetupButtonCallbacks(existingBackButton);
            }
            else
            {
                // Create and add back button if it doesn't exist
                var backButton = new Button { text = "< Back", name = "back-game-settings-button" };
                backButton.AddToClassList("menu-button");
                backButton.AddToClassList("special-button");
                backButton.style.marginTop = 20;
                backButton.clicked += ShowSettingsMenu;
                SetupButtonCallbacks(backButton);
                gameSettingsPanel.Add(backButton);
            }
        }
        else
        {
            Debug.LogWarning("Game settings panel not found");
        }
    }

    protected virtual void InitializeControlsSettings()
    {
        if (controlsSettingsPanel != null)
        {
            // Add back button to controls settings panel
            var existingBackButton = controlsSettingsPanel.Q<Button>("back-controls-settings-button");
            if (existingBackButton != null)
            {
                existingBackButton.clicked += ShowSettingsMenu;
                SetupButtonCallbacks(existingBackButton);
            }
            else
            {
                // Create and add back button if it doesn't exist
                var backButton = new Button { text = "< Back", name = "back-controls-settings-button" };
                backButton.AddToClassList("menu-button");
                backButton.AddToClassList("special-button");
                backButton.style.marginTop = 20;
                backButton.clicked += ShowSettingsMenu;
                SetupButtonCallbacks(backButton);
                controlsSettingsPanel.Add(backButton);
            }
        }
        else
        {
            Debug.LogWarning("Controls settings panel not found");
        }
    }

    protected virtual void InitializeVideoSettings()
    {
        if (graphicsSettingsPanel != null && SettingsManager.Instance != null)
        {
            // Initialize FPS limit dropdown
            fpsLimitDropdown = graphicsSettingsPanel.Q<DropdownField>("fps-limit-dropdown");
            if (fpsLimitDropdown != null)
            {
                var fpsOptions = new List<string>();
                foreach (var fps in SettingsVideoManager.Instance.FPSLimitOptions)
                {
                    fpsOptions.Add(fps == -1 ? "Unlimited" : fps.ToString());
                }

                fpsLimitDropdown.choices = fpsOptions;

                // Find the index of the current FPS limit
                int currentFpsIndex = 0;
                for (int i = 0; i < SettingsVideoManager.Instance.FPSLimitOptions.Length; i++)
                {
                    if (SettingsVideoManager.Instance.FPSLimitOptions[i] == SettingsVideoManager.Instance.FPSLimit)
                    {
                        currentFpsIndex = i;
                        break;
                    }
                }

                fpsLimitDropdown.index = currentFpsIndex;

                fpsLimitDropdown.RegisterValueChangedCallback(evt =>
                {
                    int selectedIndex = fpsLimitDropdown.index;
                    if (selectedIndex >= 0 && selectedIndex < SettingsVideoManager.Instance.FPSLimitOptions.Length)
                    {
                        SettingsVideoManager.Instance.FPSLimit = SettingsVideoManager.Instance.FPSLimitOptions[selectedIndex];
                    }
                });
            }

            // Initialize Background FPS dropdown
            backgroundFpsDropdown = graphicsSettingsPanel.Q<DropdownField>("bg-fps-dropdown");
            if (backgroundFpsDropdown != null)
            {
                var bgFpsOptions = new List<string> { "Low (30)", "Normal (60)" };
                backgroundFpsDropdown.choices = bgFpsOptions;

                // Set initial value
                backgroundFpsDropdown.index = SettingsVideoManager.Instance.BackgroundFPSLimit <= 30 ? 0 : 1;

                backgroundFpsDropdown.focusable = true;
                backgroundFpsDropdown.AddToClassList("menu-dropdown");
                backgroundFpsDropdown.RegisterValueChangedCallback(evt =>
                {
                    SettingsVideoManager.Instance.BackgroundFPSLimit = backgroundFpsDropdown.index == 0 ? 30 : 60;
                });
                backgroundFpsDropdown.RegisterCallback<KeyDownEvent>(e =>
                {
                    if (e.keyCode == KeyCode.LeftArrow)
                    {
                        int idx = Mathf.Clamp(backgroundFpsDropdown.index - 1, 0, backgroundFpsDropdown.choices.Count - 1);
                        backgroundFpsDropdown.index = idx;
                        e.StopPropagation();
                    }
                    else if (e.keyCode == KeyCode.RightArrow)
                    {
                        int idx = Mathf.Clamp(backgroundFpsDropdown.index + 1, 0, backgroundFpsDropdown.choices.Count - 1);
                        backgroundFpsDropdown.index = idx;
                        e.StopPropagation();
                    }
                });
            }

            // Find FOV slider in graphics settings
            fovSlider = graphicsSettingsPanel.Q<Slider>("fov-slider");
            fovValueLabel = graphicsSettingsPanel.Q<Label>("fov-value");

            if (fovSlider != null)
            {
                // Set initial values from settings manager
                fovSlider.lowValue = SettingsVideoManager.Instance.MinFOV;
                fovSlider.highValue = SettingsVideoManager.Instance.MaxFOV;
                fovSlider.value = SettingsVideoManager.Instance.FieldOfView;
                fovSlider.focusable = true;
                fovSlider.AddToClassList("menu-slider");

                // Update label
                if (fovValueLabel != null)
                {
                    fovValueLabel.text = $"{fovSlider.value:F0}°";
                }

                // Register change callback
                fovSlider.RegisterValueChangedCallback(evt =>
                {
                    SettingsVideoManager.Instance.FieldOfView = evt.newValue;
                    if (fovValueLabel != null)
                    {
                        fovValueLabel.text = $"{evt.newValue:F0}°";
                    }
                });
                // Keyboard adjust with left/right arrows
                fovSlider.RegisterCallback<KeyDownEvent>(e =>
                {
                    if (e.keyCode == KeyCode.LeftArrow)
                    {
                        fovSlider.value = Mathf.Max(fovSlider.lowValue, fovSlider.value - 1f);
                        e.StopPropagation();
                    }
                    else if (e.keyCode == KeyCode.RightArrow)
                    {
                        fovSlider.value = Mathf.Min(fovSlider.highValue, fovSlider.value + 1f);
                        e.StopPropagation();
                    }
                });
            }

            // VSync option (enabled by default)
            var vsyncToggle = graphicsSettingsPanel.Q<Toggle>("vsync-toggle");
            if (vsyncToggle == null)
            {
                // Create toggle if not in template
                vsyncToggle = new Toggle("VSync") { name = "vsync-toggle" };
                vsyncToggle.AddToClassList("menu-toggle");
                graphicsSettingsPanel.Add(vsyncToggle);
            }
            // Default ON
            QualitySettings.vSyncCount = QualitySettings.vSyncCount == 0 ? 1 : QualitySettings.vSyncCount;
            vsyncToggle.value = QualitySettings.vSyncCount > 0;
            vsyncToggle.RegisterValueChangedCallback(evt =>
            {
                QualitySettings.vSyncCount = evt.newValue ? 1 : 0;
            });

            // Register for settings change events
            SettingsVideoManager.Instance.OnFPSLimitChanged += OnFPSLimitChanged;
            SettingsVideoManager.Instance.OnBackgroundFPSLimitChanged += OnBackgroundFPSLimitChanged;
            SettingsVideoManager.Instance.OnFOVChanged += OnFOVChanged;

            // Add back button at the bottom of the panel
            var existingBackButton = graphicsSettingsPanel.Q<Button>("back-to-settings-button");
            if (existingBackButton != null)
            {
                existingBackButton.clicked += ShowSettingsMenu;
                SetupButtonCallbacks(existingBackButton);
            }
            else
            {
                var backButton = new Button { text = "< Back", name = "back-to-settings-button" };
                backButton.AddToClassList("menu-button");
                backButton.AddToClassList("special-button");
                backButton.style.marginTop = 20;
                backButton.clicked += ShowSettingsMenu;
                SetupButtonCallbacks(backButton);
                graphicsSettingsPanel.Add(backButton);
            }
            // Enhance FPS dropdown keyboard support and style
            if (fpsLimitDropdown != null)
            {
                fpsLimitDropdown.focusable = true;
                fpsLimitDropdown.AddToClassList("menu-dropdown");
                fpsLimitDropdown.RegisterCallback<KeyDownEvent>(e =>
                {
                    if (e.keyCode == KeyCode.LeftArrow)
                    {
                        int idx = Mathf.Clamp(fpsLimitDropdown.index - 1, 0, fpsLimitDropdown.choices.Count - 1);
                        fpsLimitDropdown.index = idx;
                        e.StopPropagation();
                    }
                    else if (e.keyCode == KeyCode.RightArrow)
                    {
                        int idx = Mathf.Clamp(fpsLimitDropdown.index + 1, 0, fpsLimitDropdown.choices.Count - 1);
                        fpsLimitDropdown.index = idx;
                        e.StopPropagation();
                    }
                });
            }
        }
        else
        {
            Debug.LogWarning("Graphics settings panel not found or SettingsManager not available");
        }
    }

    // Event handlers for settings changes
    protected virtual void OnFOVChanged(float fov)
    {
        if (fovSlider != null)
        {
            fovSlider.value = fov;
        }

        if (fovValueLabel != null)
        {
            fovValueLabel.text = $"{fov:F0}°";
        }
    }

    protected virtual void OnFPSLimitChanged(int fpsLimit)
    {
        if (fpsLimitDropdown != null)
        {
            // Find the index of the current FPS limit
            int currentFpsIndex = 0;
            for (int i = 0; i < SettingsVideoManager.Instance.FPSLimitOptions.Length; i++)
            {
                if (SettingsVideoManager.Instance.FPSLimitOptions[i] == fpsLimit)
                {
                    currentFpsIndex = i;
                    break;
                }
            }

            fpsLimitDropdown.index = currentFpsIndex;
        }
    }

    protected virtual void OnBackgroundFPSLimitChanged(int backgroundFpsLimit)
    {
        if (backgroundFpsDropdown != null)
        {
            backgroundFpsDropdown.index = backgroundFpsLimit <= 30 ? 0 : 1;
        }
    }

    private void InitializeSettingsButton(string buttonName, Action action)
    {
        Button button = settingsRoot.Q<Button>(buttonName);
        if (button != null)
        {
            button.clicked += action;
            buttonActions[button] = action;
            settingsButtons.Add(button);
            SetupButtonCallbacks(button);
            button.style.display = DisplayStyle.None;
        }
    }

    protected void SetupCursor()
    {
        customCursor = FindFirstObjectByType<CustomCursor>();
        if (customCursor == null)
        {
            Debug.LogWarning("CustomCursor not found in the scene.");
        }
    }

    protected virtual void InitializeButtons()
    {
        SetupButton("settings-button", OpenSettings);
    }

    protected void SetupButton(string buttonName, Action action)
    {
        Button button = root.Q<Button>(buttonName);
        if (button != null)
        {
            button.clicked += action;
            buttonActions[button] = action;
            menuButtons.Add(button);
            SetupButtonCallbacks(button);
        }
    }

    private void SetupButtonCallbacks(Button button)
    {
        button.RegisterCallback<MouseEnterEvent>(evt =>
        {
            if (!isUsingKeyboard)
            {
                button.AddToClassList("hover");
            }
            if (customCursor != null)
            {
                customCursor.SetInteractiveCursor();
            }
        });

        button.RegisterCallback<MouseLeaveEvent>(evt =>
        {
            if (!isUsingKeyboard)
            {
                button.RemoveFromClassList("hover");
            }
            if (customCursor != null)
            {
                customCursor.SetDefaultCursor();
            }
        });

        // Play click SFX on UI Toolkit click (works for mouse and keyboard activation)
        button.RegisterCallback<ClickEvent>(evt =>
        {
            PlayButtonClickSound();
        });
    }

    private void PlayButtonClickSound()
    {
        if (uiAudioChannel != null && buttonClickEvent != null)
        {
            var parameters = new AudioPlaybackParams
            {
                pitchMultiplier = 1f + UnityEngine.Random.Range(-0.1f, 0.1f)
            };
            uiAudioChannel.RaiseParameterizedEvent(buttonClickEvent.eventData, parameters);
        }
    }

    protected virtual void CleanupButtons()
    {
        foreach (var kvp in buttonActions)
        {
            if (kvp.Key != null)
            {
                kvp.Key.clicked -= kvp.Value;
            }
        }
        buttonActions.Clear();
        menuButtons.Clear();
        settingsButtons.Clear();
    }

    #endregion

    #region Settings Management

    protected virtual void OpenSettings()
    {
        isInSubmenu = true;
        ShowSettingsMenu();
        currentSelectedIndex = -1;
        UpdateSelectedButton();
    }

    protected virtual void ShowSettingsMenu()
    {
        if (titleLabel != null)
        {
            titleLabel.text = "Settings";
        }

        foreach (var button in menuButtons)
        {
            button.style.display = DisplayStyle.None;
        }

        if (settingsRoot != null)
        {
            settingsRoot.style.display = DisplayStyle.Flex;
            ShowSettingsButtons();
        }

        HideAllSettingsPanels();
    }

    protected void ShowSettingsButtons()
    {
        foreach (var button in settingsButtons)
        {
            button.style.display = DisplayStyle.Flex;
        }
    }

    protected virtual void ShowStartMenu()
    {
        isInSubmenu = false;

        foreach (var button in menuButtons)
        {
            button.style.display = DisplayStyle.Flex;
        }

        if (settingsRoot != null)
        {
            settingsRoot.style.display = DisplayStyle.None;
            foreach (var button in settingsButtons)
            {
                button.style.display = DisplayStyle.None;
            }
        }

        currentSelectedIndex = -1;
        UpdateSelectedButton();
    }

    protected void HideAllSettingsPanels()
    {
        if (gameSettingsPanel != null) gameSettingsPanel.style.display = DisplayStyle.None;
        if (graphicsSettingsPanel != null) graphicsSettingsPanel.style.display = DisplayStyle.None;
        if (audioSettingsPanel != null) audioSettingsPanel.style.display = DisplayStyle.None;
        if (controlsSettingsPanel != null) controlsSettingsPanel.style.display = DisplayStyle.None;
    }

    protected virtual void OpenGameplaySettings()
    {
        HideAllSettingsPanels();
        HideSettingsButtons();
        if (gameSettingsPanel != null)
        {
            gameSettingsPanel.style.display = DisplayStyle.Flex;
        }
    }

    protected void HideSettingsButtons()
    {
        foreach (var button in settingsButtons)
        {
            button.style.display = DisplayStyle.None;
        }
    }

    protected virtual void OpenGraphicsSettings()
    {
        Debug.Log("Opening graphics settings panel");
        HideAllSettingsPanels();
        HideSettingsButtons();
        if (graphicsSettingsPanel != null)
        {
            Debug.Log("Graphics panel found, setting to display");
            graphicsSettingsPanel.style.display = DisplayStyle.Flex;
        }
        else
        {
            Debug.LogError("Graphics settings panel is null!");
        }
    }

    protected virtual void OpenAudioSettings()
    {
        HideAllSettingsPanels();
        HideSettingsButtons();
        if (audioSettingsPanel != null)
        {
            audioSettingsPanel.style.display = DisplayStyle.Flex;
            Debug.Log("Audio settings panel opened");
        }
        else
        {
            Debug.LogError("Audio settings panel is null!");
        }
    }

    protected virtual void InitializeAudioSettings()
    {
        if (audioSettingsPanel != null && SettingsAudioManager.Instance != null)
        {
            // Load audio settings template from Resources
            var audioSettingsTemplate = Resources.Load<VisualTreeAsset>("SettingsSubMenu/SettingsAudio");
            if (audioSettingsTemplate != null)
            {
                // Clear any existing content
                audioSettingsPanel.Clear();

                // Instantiate the audio settings UI
                var audioSettingsUI = audioSettingsTemplate.Instantiate();
                audioSettingsPanel.Add(audioSettingsUI);

                // Initialize audio UI elements
                var masterVolumeSlider = audioSettingsPanel.Q<Slider>("master-volume-slider");
                var masterVolumeLabel = audioSettingsPanel.Q<Label>("master-volume-value");
                if (masterVolumeSlider != null)
                {
                    masterVolumeSlider.value = SettingsAudioManager.Instance.MasterVolume;
                    if (masterVolumeLabel != null)
                    {
                        masterVolumeLabel.text = $"{(int)(masterVolumeSlider.value * 100)}%";
                    }

                    masterVolumeSlider.RegisterValueChangedCallback(evt =>
                    {
                        SettingsAudioManager.Instance.MasterVolume = evt.newValue;
                        if (masterVolumeLabel != null)
                        {
                            masterVolumeLabel.text = $"{(int)(evt.newValue * 100)}%";
                        }
                    });
                    // Keyboard adjust with left/right arrows
                    masterVolumeSlider.focusable = true;
                    masterVolumeSlider.RegisterCallback<KeyDownEvent>(e =>
                    {
                        if (e.keyCode == KeyCode.LeftArrow)
                        {
                            masterVolumeSlider.value = Mathf.Max(masterVolumeSlider.lowValue, masterVolumeSlider.value - 0.05f);
                            e.StopPropagation();
                        }
                        else if (e.keyCode == KeyCode.RightArrow)
                        {
                            masterVolumeSlider.value = Mathf.Min(masterVolumeSlider.highValue, masterVolumeSlider.value + 0.05f);
                            e.StopPropagation();
                        }
                    });
                }

                var musicVolumeSlider = audioSettingsPanel.Q<Slider>("music-volume-slider");
                var musicVolumeLabel = audioSettingsPanel.Q<Label>("music-volume-value");
                if (musicVolumeSlider != null)
                {
                    musicVolumeSlider.value = SettingsAudioManager.Instance.MusicVolume;
                    if (musicVolumeLabel != null)
                    {
                        musicVolumeLabel.text = $"{(int)(musicVolumeSlider.value * 100)}%";
                    }

                    musicVolumeSlider.RegisterValueChangedCallback(evt =>
                    {
                        SettingsAudioManager.Instance.MusicVolume = evt.newValue;
                        if (musicVolumeLabel != null)
                        {
                            musicVolumeLabel.text = $"{(int)(evt.newValue * 100)}%";
                        }
                    });
                    musicVolumeSlider.focusable = true;
                    musicVolumeSlider.RegisterCallback<KeyDownEvent>(e =>
                    {
                        if (e.keyCode == KeyCode.LeftArrow)
                        {
                            musicVolumeSlider.value = Mathf.Max(musicVolumeSlider.lowValue, musicVolumeSlider.value - 0.05f);
                            e.StopPropagation();
                        }
                        else if (e.keyCode == KeyCode.RightArrow)
                        {
                            musicVolumeSlider.value = Mathf.Min(musicVolumeSlider.highValue, musicVolumeSlider.value + 0.05f);
                            e.StopPropagation();
                        }
                    });
                }

                var sfxVolumeSlider = audioSettingsPanel.Q<Slider>("sfx-volume-slider");
                var sfxVolumeLabel = audioSettingsPanel.Q<Label>("sfx-volume-value");
                if (sfxVolumeSlider != null)
                {
                    sfxVolumeSlider.value = SettingsAudioManager.Instance.SFXVolume;
                    if (sfxVolumeLabel != null)
                    {
                        sfxVolumeLabel.text = $"{(int)(sfxVolumeSlider.value * 100)}%";
                    }

                    sfxVolumeSlider.RegisterValueChangedCallback(evt =>
                    {
                        SettingsAudioManager.Instance.SFXVolume = evt.newValue;
                        if (sfxVolumeLabel != null)
                        {
                            sfxVolumeLabel.text = $"{(int)(evt.newValue * 100)}%";
                        }
                    });
                    sfxVolumeSlider.focusable = true;
                    sfxVolumeSlider.RegisterCallback<KeyDownEvent>(e =>
                    {
                        if (e.keyCode == KeyCode.LeftArrow)
                        {
                            sfxVolumeSlider.value = Mathf.Max(sfxVolumeSlider.lowValue, sfxVolumeSlider.value - 0.05f);
                            e.StopPropagation();
                        }
                        else if (e.keyCode == KeyCode.RightArrow)
                        {
                            sfxVolumeSlider.value = Mathf.Min(sfxVolumeSlider.highValue, sfxVolumeSlider.value + 0.05f);
                            e.StopPropagation();
                        }
                    });
                }

                var uiVolumeSlider = audioSettingsPanel.Q<Slider>("ui-volume-slider");
                var uiVolumeLabel = audioSettingsPanel.Q<Label>("ui-volume-value");
                if (uiVolumeSlider != null)
                {
                    uiVolumeSlider.value = SettingsAudioManager.Instance.UIVolume;
                    if (uiVolumeLabel != null)
                    {
                        uiVolumeLabel.text = $"{(int)(uiVolumeSlider.value * 100)}%";
                    }

                    uiVolumeSlider.RegisterValueChangedCallback(evt =>
                    {
                        SettingsAudioManager.Instance.UIVolume = evt.newValue;
                        if (uiVolumeLabel != null)
                        {
                            uiVolumeLabel.text = $"{(int)(evt.newValue * 100)}%";
                        }
                    });
                    uiVolumeSlider.focusable = true;
                    uiVolumeSlider.RegisterCallback<KeyDownEvent>(e =>
                    {
                        if (e.keyCode == KeyCode.LeftArrow)
                        {
                            uiVolumeSlider.value = Mathf.Max(uiVolumeSlider.lowValue, uiVolumeSlider.value - 0.05f);
                            e.StopPropagation();
                        }
                        else if (e.keyCode == KeyCode.RightArrow)
                        {
                            uiVolumeSlider.value = Mathf.Min(uiVolumeSlider.highValue, uiVolumeSlider.value + 0.05f);
                            e.StopPropagation();
                        }
                    });
                }

                var muteToggle = audioSettingsPanel.Q<Toggle>("mute-toggle");
                if (muteToggle != null)
                {
                    muteToggle.value = SettingsAudioManager.Instance.IsMuted;

                    muteToggle.RegisterValueChangedCallback(evt =>
                    {
                        SettingsAudioManager.Instance.IsMuted = evt.newValue;
                    });
                }

                // Add back button
                var backButton = audioSettingsPanel.Q<Button>("back-audio-settings-button");
                if (backButton != null)
                {
                    backButton.clicked += ShowSettingsMenu;
                    SetupButtonCallbacks(backButton);
                }
                else
                {
                    // If back button doesn't exist in the template, add one
                    backButton = new Button { text = "< Back", name = "back-audio-settings-button" };
                    backButton.AddToClassList("menu-button");
                    backButton.AddToClassList("special-button");
                    backButton.style.marginTop = 20;
                    backButton.clicked += ShowSettingsMenu;
                    SetupButtonCallbacks(backButton);
                    audioSettingsPanel.Add(backButton);
                }

                Debug.Log("Audio settings UI initialized");
            }
            else
            {
                Debug.LogError("Failed to load SettingsAudio.uxml from Resources");
            }
        }
        else
        {
            Debug.LogWarning("Audio settings panel not found or SettingsAudioManager not available");
        }
    }

    protected virtual void OpenControlSettings()
    {
        HideAllSettingsPanels();
        HideSettingsButtons();
        if (controlsSettingsPanel != null)
        {
            controlsSettingsPanel.style.display = DisplayStyle.Flex;
        }
    }

    #endregion

    #region Keyboard Navigation

    protected virtual void HandleKeyboardNavigation()
    {
        if (Input.GetKeyDown(KeyCode.UpArrow))
        {
            HandleUpNavigation();
        }
        else if (Input.GetKeyDown(KeyCode.DownArrow))
        {
            HandleDownNavigation();
        }
        else if (Input.GetKeyDown(KeyCode.Return))
        {
            HandleButtonActivation();
        }
        else if (Input.GetKeyDown(KeyCode.Backspace))
        {
            NavigateBackOneLevel();
        }
        else if (Input.GetAxisRaw("Mouse X") != 0 || Input.GetAxisRaw("Mouse Y") != 0)
        {
            HandleMouseMovement();
        }
    }

    private void NavigateBackOneLevel()
    {
        // If a subpanel (game/audio/video/controls) is open, go back to the Settings root
        bool subpanelVisible =
            (gameSettingsPanel != null && gameSettingsPanel.style.display == DisplayStyle.Flex) ||
            (graphicsSettingsPanel != null && graphicsSettingsPanel.style.display == DisplayStyle.Flex) ||
            (audioSettingsPanel != null && audioSettingsPanel.style.display == DisplayStyle.Flex) ||
            (controlsSettingsPanel != null && controlsSettingsPanel.style.display == DisplayStyle.Flex);

        if (subpanelVisible)
        {
            ShowSettingsMenu();
            return;
        }

        // If we're in settings root, go back to the start menu
        if (isInSubmenu)
        {
            ShowStartMenu();
            return;
        }

        // Otherwise at the start menu, no-op (or extend to close pause menu if needed)
    }

    private void HandleUpNavigation()
    {
        var currentList = isInSubmenu ? settingsButtons : menuButtons;
        if (!isUsingKeyboard)
        {
            isUsingKeyboard = true;
            currentSelectedIndex = currentList.Count - 1;
        }
        else
        {
            currentSelectedIndex = (currentSelectedIndex - 1 + currentList.Count) % currentList.Count;
        }
        UpdateSelectedButton();
    }

    private void HandleDownNavigation()
    {
        var currentList = isInSubmenu ? settingsButtons : menuButtons;
        if (!isUsingKeyboard)
        {
            isUsingKeyboard = true;
            currentSelectedIndex = 0;
        }
        else
        {
            currentSelectedIndex = (currentSelectedIndex + 1) % currentList.Count;
        }
        UpdateSelectedButton();
    }

    private void HandleButtonActivation()
    {
        var currentList = isInSubmenu ? settingsButtons : menuButtons;
        if (currentSelectedIndex >= 0 && currentSelectedIndex < currentList.Count)
        {
            Button selectedButton = currentList[currentSelectedIndex];
            selectedButton?.SendEvent(new ClickEvent());
        }
    }

    private void HandleMouseMovement()
    {
        if (isUsingKeyboard)
        {
            isUsingKeyboard = false;
            ClearAllSelections();
        }
    }

    protected virtual void UpdateSelectedButton()
    {
        if (!isUsingKeyboard) return;

        var currentList = isInSubmenu ? settingsButtons : menuButtons;
        foreach (var button in currentList)
        {
            button.RemoveFromClassList("selected");
            button.RemoveFromClassList("hover");
        }

        if (currentSelectedIndex >= 0 && currentSelectedIndex < currentList.Count)
        {
            var selectedButton = currentList[currentSelectedIndex];
            selectedButton.AddToClassList("selected");
            selectedButton.Focus();
        }
    }

    protected void ClearAllSelections()
    {
        foreach (var button in menuButtons)
        {
            button.RemoveFromClassList("selected");
            button.RemoveFromClassList("hover");
        }
        foreach (var button in settingsButtons)
        {
            button.RemoveFromClassList("selected");
            button.RemoveFromClassList("hover");
        }
    }

    #endregion
}
-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Runtime.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Runtime.ref.dll"
-define:UNITY_6000_2_0
-define:UNITY_6000_2
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:UNITY_6000_2_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_UNITY_CONSENT
-define:ENABLE_UNITY_CLOUD_IDENTIFIERS
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLOUD_SERVICES_ENGINE_DIAGNOSTICS
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:MIRROR
-define:MIRROR_81_OR_NEWER
-define:MIRROR_82_OR_NEWER
-define:MIRROR_83_OR_NEWER
-define:MIRROR_84_OR_NEWER
-define:MIRROR_85_OR_NEWER
-define:MIRROR_86_OR_NEWER
-define:MIRROR_89_OR_NEWER
-define:MIRROR_90_OR_NEWER
-define:MIRROR_93_OR_NEWER
-define:EDGEGAP_PLUGIN_SERVERS
-define:RealtimeCSG
-define:RealtimeCSG_1
-define:RealtimeCSG_1_6
-define:RealtimeCSG_1_6_01
-define:BAKERY_INCLUDED
-define:HDRP_1_OR_NEWER
-define:ENABLE_VR_MODULE
-define:ENABLE_XR_MODULE
-define:REFLECTION_PROBE_UPDATE_CACHED_DATA_AVAILABLE
-define:ENABLE_MATHEMATICS_1_2_1
-define:ENABLE_BURST_1_5_0_OR_NEWER
-define:HDRP_HAS_TIMELINE
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.ClothModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.IdentifiersModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.InsightsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConsentModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll"
-r:"Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d78732e851eb/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.testtools.codecoverage@205a02cbcb39/lib/ReportGenerator/ReportGeneratorMerged.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/AssemblyInfo.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/ComponentUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Compositor/AdditionalCompositorData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Compositor/AlphaInjection.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Compositor/ChromaKeying.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Compositor/CompositionFilter.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Compositor/CompositionLayer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Compositor/CompositionManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Compositor/CompositionProfile.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Compositor/CompositorCameraRegistry.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Compositor/CustomClear.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Compositor/ICompositionFilterComponent.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Compositor/ShaderProperty.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Core/CoreResources/GPUCopy.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Core/Debugging/FrameSettingsFieldAttribute.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Core/Migration/IVersionable.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Core/Migration/MigrationDescription.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Core/Migration/MigrationStep.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Core/Textures/EncodeBC6H.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Core/Textures/Texture2DAtlasDynamic.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Core/Textures/TextureCache.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Core/Textures/TextureCache2D.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Core/Textures/TextureCacheCubemap.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Core/Utilities/GeometryUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/ColorPickerDebug.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/DebugDisplay.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/DebugDisplaySettingsCamera.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/DebugDisplaySettingsDecal.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/DebugDisplayVirtualTexturing.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/DebugLightVolumes.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/DebugOverlay.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/DecalsDebug.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/FalseColorDebug.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/GPUInlineDebugDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/HDDebugDisplaySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/HDDebugDisplayStats.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/HDVolumeDebugSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/HDVolumeDebugSettings.deprecated.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/LightingDebug.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/MaterialDebug.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/MipMapDebug.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/MonitorsDebug.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/NVIDIADebugView.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/RayCountManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Debug/TransparencyDebug.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Deprecated.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Documentation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/AtmosphericScattering/AtmosphericScattering.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/AtmosphericScattering/ExponentialFog.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/AtmosphericScattering/Fog.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/AtmosphericScattering/VolumetricFog.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/DiffusionProfileList.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/GlobalIllumination.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/GlobalIlluminationUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/HDRenderPipeline.ProbeHelpers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/IndirectLightingController.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Light/HDAdditionalLightData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Light/HDAdditionalLightData.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Light/HDAdditionalLightData.Types.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Light/HDGpuLightsBuilder.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Light/HDGpuLightsBuilder.Jobs.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Light/HDGpuLightsBuilder.LightLoop.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Light/HDLightRenderDatabase.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Light/HDProcessedVisibleLightsBuilder.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Light/HDProcessedVisibleLightsBuilder.Jobs.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Light/HDProcessedVisibleLightsBuilder.LightLoop.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Light/HDShadowRequestDatabase.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Light/HDShadowRequestUpdateJob.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/LightCookieManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/LightDefinition.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/LightLoop/GlobalLightLoopSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/LightLoop/LightLoop.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/ProbeVolume/ProbeVolumeLighting.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/HDAdditionalReflectionData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/HDAdditionalReflectionData.Legacy.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/HDAdditionalReflectionData.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/HDProbe.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/HDProbe.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/HDProbeCullingResults.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/HDProbeCullState.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/HDProbeSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/HDRuntimeReflectionSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/PlanarReflectionProbe.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/PlanarReflectionProbe.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/ReflectionProbeTextureCache.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/ReflectionSystemParameters.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/Volume/InfluenceVolume.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/Volume/InfluenceVolume.Editor.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/Volume/InfluenceVolume.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/Volume/ProxyVolume.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/Volume/ProxyVolume.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/Volume/ReflectionProxyVolumeComponent.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Reflection/Volume/ShapeType.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/ScreenSpaceLighting/BilateralUpsampleDef.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/ScreenSpaceLighting/HDRenderPipeline.AmbientOcclusion.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/ScreenSpaceLighting/HDRenderPipeline.ScreenSpaceGlobalIllumination.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/ScreenSpaceLighting/RayMarchingFallbackHierarchy.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/ScreenSpaceLighting/ScreenSpaceAmbientOcclusion.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/ScreenSpaceLighting/ScreenSpaceGlobalIllumination.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/ScreenSpaceLighting/ScreenSpaceReflection.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/ScreenSpaceLighting/ScreenSpaceRefraction.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/ScreenSpaceLighting/ShaderVariablesAmbientOcclusion.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/ScreenSpaceLighting/ShaderVariablesScreenSpaceReflection.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/AdditionalShadowData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/ContactShadows.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/HDCachedShadowAtlas.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/HDCachedShadowManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/HDDynamicShadowAtlas.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/HDRenderPipeline.ScreenSpaceShadows.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/HDRenderPIpeline.ScreenSpaceShadowsArea.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/HDRenderPipeline.ScreenSpaceShadowsDirectional.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/HDRenderPipeline.ScreenSpaceShadowsPunctual.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/HDShadowAtlas.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/HDShadowCullingUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/HDShadowManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/HDShadowSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/HDShadowUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/Shadow/MicroShadowing.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/SphericalHarmonics.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricClouds/HDRenderPipeline.VolumetricClouds.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricClouds/HDRenderPipeline.VolumetricCloudsAccumulation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricClouds/HDRenderPipeline.VolumetricCloudsFullResolution.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricClouds/HDRenderPipeline.VolumetricCloudsLighting.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricClouds/HDRenderPipeline.VolumetricCloudsLowResolution.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricClouds/HDRenderPipeline.VolumetricCloudsMap.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricClouds/HDRenderPipeline.VolumetricCloudsShadows.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricClouds/HDRenderPipeline.VolumetricCloudsSky.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricClouds/VolumetricClouds.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricClouds/VolumetricClouds.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricClouds/VolumetricCloudsDef.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricLighting/HDRenderPipeline.VolumetricLighting.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricLighting/LocalVolumetricFog.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricLighting/LocalVolumetricFog.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricLighting/LocalVolumetricFogManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Lighting/VolumetricLighting/VolumetricLightingController.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/AxF/AxF.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/AxF/AxFAPI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Builtin/BuiltinData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Decal/Decal.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Decal/DecalAPI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Decal/DecalProjector.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Decal/DecalProjector.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Decal/DecalSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Decal/DecalSystem.Jobs.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Decal/GlobalDecalSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/DecalMeshBiasTypeEnum.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/DiffusionProfile/DiffusionProfileSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/DiffusionProfile/DiffusionProfileSettings.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Eye/Eye.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Eye/EyeCausticLUTGen.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Fabric/Fabric.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Fabric/IBLFilterCharlie.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/FogVolume/FogVolumeAPI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/GGXConvolution/GGXConvolution.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/GGXConvolution/IBLFilterGGX.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Hair/Hair.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/IBLFilterBSDF.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LayeredLit/LayeredLitAPI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Lit/BaseLitAPI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Lit/Lit.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Lit/LitAPI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/BRDF/BRDF_Charlie.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/BRDF/BRDF_CookTorrance.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/BRDF/BRDF_Disney.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/BRDF/BRDF_GGX.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/BRDF/BRDF_Interface.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/BRDF/BRDF_KajiyaKaySpecular.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/BRDF/BRDF_Marschner.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/BRDF/BRDF_OrenNayar.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/BRDF/BRDF_Ward.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/Generated/LtcData.BRDF_Charlie.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/Generated/LtcData.BRDF_CookTorrance.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/Generated/LtcData.BRDF_Disney.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/Generated/LtcData.BRDF_GGX.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/Generated/LtcData.BRDF_KajiyaKaySpecular.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/Generated/LtcData.BRDF_OrenNayar.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/Generated/LtcData.BRDF_Ward.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/LTCAreaLight/LTCAreaLight.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/MaterialBlendModeEnum.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/MaterialExtension.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/MaterialExternalReferences.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/PreIntegratedFGD/PreIntegratedFGD.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/RenderPipelineMaterial.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/ShaderGraphAPI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/SixWayLit/SixWayAPI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/SixWayLit/SixWaySmokeLit.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/SphericalCapPivot/PivotData.GGX.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/SphericalCapPivot/SPTDistribution.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/StackLit/StackLit.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/SubsurfaceScattering/SubSurfaceScattering.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/TerrainLit/TerrainLitAPI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Unlit/BaseUnlitAPI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Unlit/Unlit.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Unlit/UnlitAPI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/VTBufferManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Water/Water.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Material/Water/WaterDecalAPI.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PackageInfo.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/Bloom.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/ChannelMixer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/ChromaticAberration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/ColorAdjustments.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/ColorCurves.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/DepthOfField.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/Exposure.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/FilmGrain.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/LensDistortion.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/LiftGammaGain.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/MotionBlur.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/PaniniProjection.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/ScreenSpaceLensFlare.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/ShadowsMidtonesHighlights.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/SplitToning.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/Tonemapping.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/Vignette.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Components/WhiteBalance.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/CustomPostProcessing/CustomPostProcessInjectionPoint.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/CustomPostProcessing/CustomPostProcessVolumeComponent.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/CustomPostProcessing/CustomPostProcessVolumeComponentList.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/GlobalPostProcessSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/PostProcessing/Shaders/UberPostFeatures.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Accumulation/DenoisePass.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Accumulation/SubFrameManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Camera/HDAdditionalCameraData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Camera/HDAdditionalCameraData.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Camera/HDCamera.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Camera/HDCameraFrameHistoryType.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/CullingGroupManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/GlobalGPUResidentDrawerSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/GlobalLightingQualitySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/GlobalLowResolutionTransparencySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/GlobalPostProcessingQualitySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDAdditionalMeshRendererSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDComputeThickness.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDDynamicResolution.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDDynamicResolutionPlatformCapabilities.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDProfileId.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipeline.CloudBackground.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipeline.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipeline.Debug.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipeline.LightLoop.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipeline.LookDev.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipeline.PostProcess.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipeline.Prepass.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipeline.RenderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipeline.RenderGraphUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipeline.STP.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipeline.SubsurfaceScattering.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipeline.VolumetricCloud.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipeline.Vrs.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipelineAsset.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipelineAsset.DefaultResources.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipelineAsset.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipelineAsset.Prefiltering.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipelineGlobalSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipelineGlobalSettings.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipelineResources.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderPipelineRuntimeResources.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDRenderQueue.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDStencilUsage.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/HDStringConstants.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/LineRendering/Core/LineRendering.Budgets.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/LineRendering/Core/LineRendering.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/LineRendering/Core/LineRendering.Data.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/LineRendering/Core/LineRendering.InstanceManagement.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/LineRendering/Core/LineRendering.ShaderIDs.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/LineRendering/Core/LineRendering.ShadingAtlas.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/LineRendering/Core/LineRendering.Utilities.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/LineRendering/Core/RenderPass/LineRendering.Pass.Geometry.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/LineRendering/Core/RenderPass/LineRendering.Pass.Rasterization.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/LineRendering/HDRenderPipeline.LineRendering.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/LineRendering/HDRenderPipeline.LineRendering.VolumeComponent.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/MRTBufferManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/PathTracing/PathTracing.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/HDDiffuseDenoiser.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/HDDiffuseShadowDenoiser.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/HDRaytracingLightCluster.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/HDRaytracingManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/HDRaytracingManager.HDRTASManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/HDReflectionDenoiser.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/HDRenderPipeline.RaytracingAmbientOcclusion.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/HDRenderPipeline.RaytracingDeferredLightLoop.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/HDRenderPipeline.RaytracingIndirectDiffuse.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/HDRenderPipeline.RaytracingRecursiveRenderer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/HDRenderPipeline.RaytracingReflection.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/HDRenderPipeline.RaytracingSubsurfaceScattering.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/HDTemporalFilter.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/LightCluster.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/RayCastingMode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/RayTracingFallbackHierarchy.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/RayTracingMode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/RayTracingSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/ReblurDenoiser.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/RecursiveRendering.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/Shaders/ShaderVariablesRaytracing.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Raytracing/Shaders/ShaderVariablesRaytracingLightLoop.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/AOV/AOVBuffers.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/AOV/AOVRequest.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/AOV/AOVRequestBuilder.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/AOV/AOVRequestData.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/AOV/AOVRequestDataCollection.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/AOV/RenderOutputProperties.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/CustomPass/CustomPass.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/CustomPass/CustomPassContext.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/CustomPass/CustomPassInjectionPoint.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/CustomPass/CustomPassUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/CustomPass/CustomPassVolume.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/DepthPyramidConstants.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/DLSSPass.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/DrawRenderersCustomPass.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/FSR2Pass.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/FullScreenCustomPass.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/MipGenerator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/ObjectIDCustomPass.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/UpscalerUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/RenderPass/VrsCustomPass.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/SceneViewDrawMode.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Settings/CaptureSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Settings/FrameSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Settings/FrameSettings.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Settings/FrameSettingsDefaults.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Settings/FrameSettingsHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Settings/RenderPipelineSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Settings/ScalableSetting.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Settings/ScalableSettingSchema.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Settings/ScalableSettingSchemaId.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Settings/ScalableSettingValue.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/ShaderPass/ShaderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Utility/BlueNoise.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Utility/HDUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Utility/ReflectionUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Utility/Texture3DAtlas.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/Utility/Texture3DAtlasDynamic.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/VirtualTexturingSettingsSRP.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/WorldLightCulling.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/WorldLightManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipeline/XR/GlobalXRSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipelineResources/ComputeMaterialLibrary.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/RenderPipelineResources/HDProjectSettingsReadOnlyBase.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Scripting/GameObjectExtension.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/AnalyticDerivativeSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/ColorGradingSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/CustomPostProcessOrdersSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/DiffusionProfileDefaultSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/HDRenderPipelineEditorAssets.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/HDRenderPipelineEditorMaterials.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/HDRenderPipelineEditorShaders.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/HDRenderPipelineEditorTextures.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/HDRenderPipelineRuntimeAssets.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/HDRenderPipelineRuntimeMaterials.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/HDRenderPipelineRuntimeShaders.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/HDRenderPipelineRuntimeTextures.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/HDRPDefaultVolumeProfileSetting.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/HDRPRayTracingResources.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/LensSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/LookDevVolumeProfileSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/RenderGraphSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/RenderingPathFrameSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Settings/SpecularFadeSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/ShaderLibrary/ShaderVariablesGlobal.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/ShaderLibrary/ShaderVariablesXR.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/CloudSystem/CloudLayer/CloudLayer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/CloudSystem/CloudLayer/CloudLayerRenderer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/CloudSystem/CloudRenderer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/CloudSystem/CloudSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/GradientSky/GradientSky.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/GradientSky/GradientSkyRenderer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/HDRISky/HDRISky.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/HDRISky/HDRISky.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/HDRISky/HDRISkyRenderer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/PhysicallyBasedSky/PhysicallyBasedSky.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/PhysicallyBasedSky/PhysicallyBasedSky.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/PhysicallyBasedSky/PhysicallyBasedSkyRenderer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/PhysicallyBasedSky/ShaderVariablesPhysicallyBasedSky.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/SkyManager.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/SkyRenderer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/SkyRenderingContext.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/SkySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/SkyUpdateContext.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/StaticLightingSky.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/VisualEnvironment.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Sky/VisualEnvironment.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Tools/ColorCheckerTool.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/CameraPositionSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/CameraSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/CameraSettingsUtilities.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/CopyFilter.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/DiffusionProfileHashTable.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/HDAdditionalSceneViewSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/HDBakingUtilities.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/HDRenderPipelinePreferences.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/HDRenderUtilities.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/HDTextureUtilities.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/ProbeCameraCache.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/ProbeCapturePositionSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/ProbeSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/ProbeSettingsUtilities.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/SceneObjectIDMap.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/TypeInfo.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/VolumeComponentWithQuality.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Utilities/VolumeUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/VFXGraph/Utility/PropertyBinders/HDRPCameraBinder.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/AsyncTextureSynchronizer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/HDRenderPipeline.WaterSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/HDRenderPipeline.WaterSystem.Debug.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/HDRenderPipeline.WaterSystem.Decals.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/HDRenderPipeline.WaterSystem.Simulation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/HDRenderPipeline.WaterSystem.SimulationCPU.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/HDRenderPipeline.WaterSystem.SimulationCPU.Deformation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/HDRenderPipeline.WaterSystem.SimulationCPU.Search.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/HDRenderPipeline.WaterSystem.SimulationCPU.Utilities.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/HDRenderPipeline.WaterSystem.Underwater.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/HDRenderPipeline.WaterSystem.Utilities.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/HDRenderPipeline.WaterSystem.Wireframe.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/ShaderVariablesWater.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/Timeline/WaterPlayableAsset.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/Timeline/WaterSurfaceBehaviour.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/Timeline/WaterSurfacePlayableBehaviour.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/Timeline/WaterSurfaceTrack.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterAmplitudeEvaluator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterDecal/Legacy/WaterDeformer.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterDecal/Legacy/WaterFoamGenerator.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterDecal/Legacy/WaterFoamGenerator.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterDecal/WaterDecal.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterExcluder.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterRendering.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterSurface.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterSurface/WaterSurface.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterSurface/WaterSurface.Current.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterSurface/WaterSurface.Deformation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterSurface/WaterSurface.Foam.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterSurface/WaterSurface.Presets.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterSurface/WaterSurface.Simulation.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterSurface/WaterSurface.WaterMask.cs"
"Library/PackageCache/com.unity.render-pipelines.high-definition@28765669b6fe/Runtime/Water/WaterSystemDef.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Runtime.UnityAdditionalFile.txt"
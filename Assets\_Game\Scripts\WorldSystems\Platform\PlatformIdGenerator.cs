using System.Text.RegularExpressions;
using UnityEngine;

public static class PlatformIdGenerator
{
    public static string GeneratePlatformId(Transform transform, string customId = null)
    {
        if (!string.IsNullOrEmpty(customId))
        {
            return SanitizeId(customId);
        }

        string sceneName = transform.gameObject.scene.name;
        string guid8 = System.Guid.NewGuid().ToString("N").Substring(0, 8).ToUpperInvariant();
        return $"KP_{sceneName}_{guid8}";
    }

    private static string SanitizeId(string id)
    {
        return Regex.Replace(id, @"[^a-zA-Z0-9_-]", "_");
    }
}



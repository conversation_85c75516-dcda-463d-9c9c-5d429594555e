using System.Collections.Generic;
using UnityEngine;
using KinematicCharacterController;

/// <summary>
/// Centralized manager to pause/resume all kinematic platforms before/after Time.timeScale changes.
/// </summary>
public class PlatformPauseManager : MonoBehaviour
{
    private static PlatformPauseManager _instance;
    public static PlatformPauseManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindFirstObjectByType<PlatformPauseManager>();
                if (_instance == null)
                {
                    GameObject go = new GameObject("PlatformPauseManager");
                    _instance = go.AddComponent<PlatformPauseManager>();
                }
            }
            return _instance;
        }
    }

    private readonly List<KinematicPlatform> _allPlatforms = new List<KinematicPlatform>();
    private readonly List<KinematicPlatformPassengerZone> _allZones = new List<KinematicPlatformPassengerZone>();

    private readonly Dictionary<KinematicPlatform, PlatformPauseState> _pauseStates = new Dictionary<KinematicPlatform, PlatformPauseState>();
    private bool _isPaused = false;

    private class PlatformPauseState
    {
        public bool wasMoving;
        public bool wasWaiting;
        public Vector3 savedVelocity;
        public Vector3 savedAngularVelocity;
    }

    private void Awake()
    {
        if (_instance != null && _instance != this)
        {
            Destroy(gameObject);
            return;
        }
        _instance = this;
        DontDestroyOnLoad(gameObject);
    }

    private void Start()
    {
        RefreshPlatformReferences();
    }

    public void RefreshPlatformReferences()
    {
        _allPlatforms.Clear();
        _allZones.Clear();

        _allPlatforms.AddRange(FindObjectsByType<KinematicPlatform>(FindObjectsSortMode.None));
        _allZones.AddRange(FindObjectsByType<KinematicPlatformPassengerZone>(FindObjectsSortMode.None));
    }

    /// <summary>
    /// Call this BEFORE setting Time.timeScale = 0 in your pause menu
    /// </summary>
    public void PauseAllPlatforms()
    {
        if (_isPaused) return;
        _isPaused = true;
        _pauseStates.Clear();

        // Lock zones first
        foreach (var zone in _allZones)
        {
            if (zone != null)
            {
                zone.SetPaused(true);
            }
        }

        // Stop platforms and cache state
        foreach (var platform in _allPlatforms)
        {
            if (platform == null) continue;

            var state = new PlatformPauseState
            {
                wasMoving = platform.IsMoving,
                wasWaiting = platform.IsWaiting
            };

            var mover = platform.GetComponent<PhysicsMover>();
            if (mover != null)
            {
                state.savedVelocity = mover.Velocity;
                state.savedAngularVelocity = mover.AngularVelocity;
            }

            _pauseStates[platform] = state;

            platform.StopMoving();
            platform.SetSleepMode(true);

            var rb = platform.GetComponent<Rigidbody>();
            if (rb != null)
            {
                rb.linearVelocity = Vector3.zero;
                rb.angularVelocity = Vector3.zero;
                rb.isKinematic = true;
            }
        }
    }

    /// <summary>
    /// Call this AFTER setting Time.timeScale back to 1 when unpausing
    /// </summary>
    public void ResumeAllPlatforms()
    {
        if (!_isPaused) return;
        _isPaused = false;

        foreach (var platform in _allPlatforms)
        {
            if (platform == null) continue;

            platform.SetSleepMode(false);

            var rb = platform.GetComponent<Rigidbody>();
            if (rb != null)
            {
                rb.isKinematic = true;
            }

            if (_pauseStates.TryGetValue(platform, out var state))
            {
                if (state.wasMoving)
                {
                    platform.StartMoving();
                }
            }
        }

        foreach (var zone in _allZones)
        {
            if (zone != null)
            {
                zone.SetPaused(false);
            }
        }

        _pauseStates.Clear();
    }

    public void EmergencyStopAllPlatforms()
    {
        foreach (var platform in _allPlatforms)
        {
            if (platform == null) continue;
            platform.StopMoving();
            platform.SetSleepMode(true);
            var rb = platform.GetComponent<Rigidbody>();
            if (rb != null)
            {
                rb.linearVelocity = Vector3.zero;
                rb.angularVelocity = Vector3.zero;
            }
        }
    }
}



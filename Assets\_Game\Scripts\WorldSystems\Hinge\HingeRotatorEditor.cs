#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(HingeRotator))]
[CanEditMultipleObjects]
public class HingeRotatorEditor : Editor
{
    private HingeRotator hinge;

    // Serialized props for improved inspector
    private SerializedProperty authoringModeProp;
    private SerializedProperty movingPartProp;
    private SerializedProperty pivotModeProp;
    private SerializedProperty pivotMarkerProp;
    private SerializedProperty customPivotLocalPositionProp;
    private SerializedProperty autoCreatePivotAtAwakeProp;

    private SerializedProperty hingeAxisProp;
    private SerializedProperty invertAxisProp;
    private SerializedProperty closedAngleProp;
    private SerializedProperty openAngleProp;
    private SerializedProperty startOpenProp;

    private SerializedProperty useDurationProp;
    private SerializedProperty openDurationProp;
    private SerializedProperty closeDurationProp;
    private SerializedProperty angularSpeedProp;
    private SerializedProperty easingProp;
    private SerializedProperty customEaseCurveProp;
    private SerializedProperty useUnscaledTimeProp;

    private SerializedProperty displayNameProp;
    private SerializedProperty isLockedProp;

    private SerializedProperty audioChannelProp;
    private SerializedProperty openStartSoundProp;
    private SerializedProperty openEndSoundProp;
    private SerializedProperty closeStartSoundProp;
    private SerializedProperty closeEndSoundProp;
    private SerializedProperty lockedSoundProp;
    private SerializedProperty movingLoopSoundProp;
    private SerializedProperty movingLoopFadeOutTimeProp;

    private bool showSetup = true;
    private bool showRotation = true;
    private bool showMotion = true;
    private bool showInteraction = false;
    private bool showAudio = false;
    private bool showScenePivotTools = true;

    private void OnEnable()
    {
        hinge = (HingeRotator)target;
        if (hinge != null && !GetEditorFlag())
        {
            SetEditorPivotWorldPos(GetCurrentPivotWorld());
        }

        authoringModeProp = serializedObject.FindProperty("authoringMode");
        movingPartProp = serializedObject.FindProperty("movingPart");
        pivotModeProp = serializedObject.FindProperty("pivotMode");
        pivotMarkerProp = serializedObject.FindProperty("pivotMarker");
        customPivotLocalPositionProp = serializedObject.FindProperty("customPivotLocalPosition");
        autoCreatePivotAtAwakeProp = serializedObject.FindProperty("autoCreatePivotAtAwake");

        hingeAxisProp = serializedObject.FindProperty("hingeAxis");
        invertAxisProp = serializedObject.FindProperty("invertAxis");
        closedAngleProp = serializedObject.FindProperty("closedAngle");
        openAngleProp = serializedObject.FindProperty("openAngle");
        startOpenProp = serializedObject.FindProperty("startOpen");

        useDurationProp = serializedObject.FindProperty("useDuration");
        openDurationProp = serializedObject.FindProperty("openDuration");
        closeDurationProp = serializedObject.FindProperty("closeDuration");
        angularSpeedProp = serializedObject.FindProperty("angularSpeed");
        easingProp = serializedObject.FindProperty("easing");
        customEaseCurveProp = serializedObject.FindProperty("customEaseCurve");
        useUnscaledTimeProp = serializedObject.FindProperty("useUnscaledTime");

        displayNameProp = serializedObject.FindProperty("displayName");
        isLockedProp = serializedObject.FindProperty("isLocked");

        audioChannelProp = serializedObject.FindProperty("audioChannel");
        openStartSoundProp = serializedObject.FindProperty("openStartSound");
        openEndSoundProp = serializedObject.FindProperty("openEndSound");
        closeStartSoundProp = serializedObject.FindProperty("closeStartSound");
        closeEndSoundProp = serializedObject.FindProperty("closeEndSound");
        lockedSoundProp = serializedObject.FindProperty("lockedSound");
        movingLoopSoundProp = serializedObject.FindProperty("movingLoopSound");
        movingLoopFadeOutTimeProp = serializedObject.FindProperty("movingLoopFadeOutTime");
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        var h = (HingeRotator)target;

        DrawAuthoringToolbar(h);

        // Manual Pivot Authoring at the top
        EditorGUILayout.Space(6);
        showScenePivotTools = EditorGUILayout.BeginFoldoutHeaderGroup(showScenePivotTools, "Manual Pivot Authoring (Scene)");
        if (showScenePivotTools)
        {
            using (new EditorGUI.DisabledScope(Application.isPlaying))
            {
                if (!GetEditorFlag())
                {
                    if (GUILayout.Button("Edit Pivot"))
                    {
                        BeginPivotEdit();
                    }
                }
                else
                {
                    EditorGUILayout.HelpBox("Drag the gizmo in the Scene view to place the pivot. Ctrl+Click in Scene to place on colliders. Press Apply Pivot when done.", MessageType.Info);
                    var pos = GetEditorPivotWorldPos();
                    pos = EditorGUILayout.Vector3Field("Pivot World Position", pos);
                    SetEditorPivotWorldPos(pos);

                    using (new EditorGUILayout.HorizontalScope())
                    {
                        if (GUILayout.Button("Apply Pivot"))
                        {
                            ApplyManualPivot();
                        }
                        if (GUILayout.Button("Cancel"))
                        {
                            CancelPivotEdit();
                        }
                    }
                }
            }
        }
        EditorGUILayout.EndFoldoutHeaderGroup();

        EditorGUILayout.Space(6);
        showSetup = EditorGUILayout.BeginFoldoutHeaderGroup(showSetup, "Setup");
        if (showSetup)
        {
            EditorGUILayout.PropertyField(authoringModeProp);
            EditorGUILayout.PropertyField(movingPartProp);

            var authoringMode = (HingeRotator.AuthoringMode)authoringModeProp.enumValueIndex;
            if (authoringMode == HingeRotator.AuthoringMode.Manual)
            {
                EditorGUILayout.PropertyField(pivotModeProp);
                if ((HingeRotator.PivotMode)pivotModeProp.enumValueIndex == HingeRotator.PivotMode.UseCustomPivotParent)
                {
                    EditorGUILayout.PropertyField(pivotMarkerProp);
                    EditorGUILayout.PropertyField(customPivotLocalPositionProp);
                    EditorGUILayout.PropertyField(autoCreatePivotAtAwakeProp);

                    if (GUILayout.Button("Create Pivot Parent Now"))
                    {
                        h.SendMessage("ContextCreatePivotNow", SendMessageOptions.DontRequireReceiver);
                    }
                }
            }
            else
            {
                EditorGUILayout.HelpBox("Automatic mode uses Saved Closed/Open poses to build the hinge pivot and angle.", MessageType.Info);
                if (GUILayout.Button("Build From Saved Poses"))
                {
                    h.Editor_BuildFromSavedPoses();
                }
            }
        }
        EditorGUILayout.EndFoldoutHeaderGroup();

        EditorGUILayout.Space(4);
        showRotation = EditorGUILayout.BeginFoldoutHeaderGroup(showRotation, "Rotation");
        if (showRotation)
        {
            EditorGUILayout.PropertyField(hingeAxisProp);
            EditorGUILayout.PropertyField(invertAxisProp);
            EditorGUILayout.PropertyField(closedAngleProp);
            EditorGUILayout.PropertyField(openAngleProp);
            EditorGUILayout.PropertyField(startOpenProp);

            using (new EditorGUILayout.HorizontalScope())
            {
                if (GUILayout.Button("Set Current As Closed"))
                {
                    h.SendMessage("ContextSetClosed", SendMessageOptions.DontRequireReceiver);
                    EditorUtility.SetDirty(h);
                }
                if (GUILayout.Button("Set Current As Open"))
                {
                    h.SendMessage("ContextSetOpen", SendMessageOptions.DontRequireReceiver);
                    EditorUtility.SetDirty(h);
                }
            }
        }
        EditorGUILayout.EndFoldoutHeaderGroup();

        EditorGUILayout.Space(4);
        showMotion = EditorGUILayout.BeginFoldoutHeaderGroup(showMotion, "Motion");
        if (showMotion)
        {
            EditorGUILayout.PropertyField(useDurationProp);
            if (useDurationProp.boolValue)
            {
                EditorGUILayout.PropertyField(openDurationProp);
                EditorGUILayout.PropertyField(closeDurationProp);
            }
            else
            {
                EditorGUILayout.PropertyField(angularSpeedProp);
            }
            EditorGUILayout.PropertyField(easingProp);
            if ((HingeRotator.EasingType)easingProp.enumValueIndex == HingeRotator.EasingType.Custom)
            {
                EditorGUILayout.PropertyField(customEaseCurveProp);
            }
            EditorGUILayout.PropertyField(useUnscaledTimeProp);
        }
        EditorGUILayout.EndFoldoutHeaderGroup();

        EditorGUILayout.Space(4);
        showInteraction = EditorGUILayout.BeginFoldoutHeaderGroup(showInteraction, "Interaction");
        if (showInteraction)
        {
            EditorGUILayout.PropertyField(displayNameProp);
            EditorGUILayout.PropertyField(isLockedProp);
        }
        EditorGUILayout.EndFoldoutHeaderGroup();

        EditorGUILayout.Space(4);
        showAudio = EditorGUILayout.BeginFoldoutHeaderGroup(showAudio, "Audio");
        if (showAudio)
        {
            EditorGUILayout.PropertyField(audioChannelProp);
            EditorGUILayout.PropertyField(openStartSoundProp);
            EditorGUILayout.PropertyField(openEndSoundProp);
            EditorGUILayout.PropertyField(closeStartSoundProp);
            EditorGUILayout.PropertyField(closeEndSoundProp);
            EditorGUILayout.PropertyField(lockedSoundProp);
            EditorGUILayout.PropertyField(movingLoopSoundProp);
            EditorGUILayout.PropertyField(movingLoopFadeOutTimeProp);
        }
        EditorGUILayout.EndFoldoutHeaderGroup();



        serializedObject.ApplyModifiedProperties();
    }

    private void DrawAuthoringToolbar(HingeRotator h)
    {
        EditorGUILayout.Space(4);
        EditorGUILayout.LabelField("Hinge Authoring", EditorStyles.boldLabel);
        using (new EditorGUILayout.HorizontalScope())
        {
            if (GUILayout.Button("Save Closed Pose")) h.Editor_SaveClosedPose();
            if (GUILayout.Button("Save Open Pose")) h.Editor_SaveOpenPose();
            if (GUILayout.Button("Build From Poses")) h.Editor_BuildFromSavedPoses();
        }
        using (new EditorGUILayout.HorizontalScope())
        {
            if (GUILayout.Button("Move To Closed")) h.SendMessage("Author_MoveToClosed", SendMessageOptions.DontRequireReceiver);
            if (GUILayout.Button("Move To Open")) h.SendMessage("Author_MoveToOpen", SendMessageOptions.DontRequireReceiver);
            if (GUILayout.Button("Reset Hinge Setup")) h.SendMessage("Author_ResetHingeSetup", SendMessageOptions.DontRequireReceiver);
            if (GUILayout.Button("Clear Saved Poses")) h.SendMessage("Author_ClearSavedPoses", SendMessageOptions.DontRequireReceiver);
        }
        EditorGUILayout.Space(4);
        EditorGUILayout.HelpBox("Use Save Closed/Open on the moving mesh, then Build to auto-create a correct hinge pivot.", MessageType.None);
    }

    private void OnSceneGUI()
    {
        if (hinge == null || !GetEditorFlag()) return;

        EditorGUI.BeginChangeCheck();
        var handlePos = Handles.PositionHandle(GetEditorPivotWorldPos(), Quaternion.identity);
        if (EditorGUI.EndChangeCheck())
        {
            Undo.RecordObject(hinge, "Move Hinge Pivot Handle");
            SetEditorPivotWorldPos(handlePos);
            EditorUtility.SetDirty(hinge);
        }

        Event e = Event.current;
        if (e.type == EventType.MouseDown && e.control && e.button == 0)
        {
            Ray ray = HandleUtility.GUIPointToWorldRay(e.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit))
            {
                Undo.RecordObject(hinge, "Place Hinge Pivot by Click");
                SetEditorPivotWorldPos(hit.point);
                EditorUtility.SetDirty(hinge);
                e.Use();
            }
        }

        Handles.color = new Color(1f, 0.6f, 0f, 0.9f);
        Handles.SphereHandleCap(0, GetEditorPivotWorldPos(), Quaternion.identity, HandleUtility.GetHandleSize(GetEditorPivotWorldPos()) * 0.1f, EventType.Repaint);
        Handles.DrawLine(GetEditorPivotWorldPos(), GetEditorPivotWorldPos() + Vector3.up * 0.5f);
    }

    private void BeginPivotEdit()
    {
        Undo.RecordObject(hinge, "Begin Pivot Edit");
        SetEditorPivotWorldPos(GetCurrentPivotWorld());
        SetEditorFlag(true);
        EditorUtility.SetDirty(hinge);
        SceneView.RepaintAll();
    }

    private void CancelPivotEdit()
    {
        Undo.RecordObject(hinge, "Cancel Pivot Edit");
        SetEditorFlag(false);
        EditorUtility.SetDirty(hinge);
        SceneView.RepaintAll();
    }

    private void ApplyManualPivot()
    {
        if (hinge == null || hinge.transform == null) return;

        Undo.IncrementCurrentGroup();
        int group = Undo.GetCurrentGroup();
        Undo.SetCurrentGroupName("Apply Manual Hinge Pivot");

        var movingPart = hinge.EditorMovingPart != null ? hinge.EditorMovingPart : hinge.transform;
        Transform originalParent = movingPart.parent;

        if (hinge.EditorRuntimePivotParent == null)
        {
            var pivotGo = new GameObject(movingPart.name + "_HingePivot");
            Undo.RegisterCreatedObjectUndo(pivotGo, "Create Hinge Pivot");
            hinge.EditorRuntimePivotParent = pivotGo.transform;
        }

        if (originalParent != null && hinge.EditorRuntimePivotParent.parent != originalParent)
        {
            Undo.SetTransformParent(hinge.EditorRuntimePivotParent, originalParent, "Parent Hinge Pivot");
        }

        Undo.RecordObject(hinge.EditorRuntimePivotParent, "Move Hinge Pivot");
        hinge.EditorRuntimePivotParent.position = GetEditorPivotWorldPos();
        hinge.EditorRuntimePivotParent.rotation = movingPart.rotation;

        if (movingPart.parent != hinge.EditorRuntimePivotParent)
        {
            Undo.SetTransformParent(movingPart, hinge.EditorRuntimePivotParent, "Reparent Moving Part to Pivot");
        }

        Undo.RecordObject(hinge, "Configure Hinge for Manual Pivot");
        hinge.EditorPivotMode = HingeRotator.PivotMode.UseCustomPivotParent;
        hinge.EditorAuthoringMode = HingeRotator.AuthoringMode.Manual;
        hinge.EditorRotationRoot = hinge.EditorRuntimePivotParent;
        hinge.EditorInitialLocalRotation = hinge.EditorRotationRoot.localRotation;
        hinge.EditorApplyAngle(hinge.EditorStartOpen ? hinge.EditorOpenAngle : hinge.EditorClosedAngle);
        hinge.EditorSolvedHasData = false;

        SetEditorFlag(false);
        EditorUtility.SetDirty(hinge);
        Undo.CollapseUndoOperations(group);
        SceneView.RepaintAll();
    }

    private Vector3 GetCurrentPivotWorld()
    {
        if (hinge.EditorRuntimePivotParent != null)
            return hinge.EditorRuntimePivotParent.position;
        if (hinge.EditorPivotMarker != null)
            return hinge.EditorPivotMarker.position;
        var movingPart = hinge.EditorMovingPart != null ? hinge.EditorMovingPart : hinge.transform;
        var customLocal = hinge.EditorCustomPivotLocalPosition;
        return movingPart.TransformPoint(customLocal);
    }

    private bool GetEditorFlag()
    {
        var f = typeof(HingeRotator).GetField("editorPivotEditMode", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        return f != null && (bool)f.GetValue(hinge);
    }

    private void SetEditorFlag(bool value)
    {
        var f = typeof(HingeRotator).GetField("editorPivotEditMode", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        f?.SetValue(hinge, value);
    }

    private Vector3 GetEditorPivotWorldPos()
    {
        var f = typeof(HingeRotator).GetField("editorPivotWorldPos", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        return f != null ? (Vector3)f.GetValue(hinge) : Vector3.zero;
    }

    private void SetEditorPivotWorldPos(Vector3 value)
    {
        var f = typeof(HingeRotator).GetField("editorPivotWorldPos", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        f?.SetValue(hinge, value);
    }
}
#endif



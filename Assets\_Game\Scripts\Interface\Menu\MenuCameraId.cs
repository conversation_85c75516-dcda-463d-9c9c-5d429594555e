using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class MenuCameraId : MonoBehaviour
{
    [SerializeField]
    private string uniqueId = "";

    public string UniqueId => uniqueId;

    private void Awake()
    {
        // Only generate ID if it's missing and we're not in editor play mode
        // This prevents different GUIDs between editor and build
#if UNITY_EDITOR
        if (!Application.isPlaying)
        {
            EnsureId();
        }
#else
        EnsureId();
#endif
    }

#if UNITY_EDITOR
    private void OnValidate()
    {
        // Only auto-generate in editor when not playing
        if (!Application.isPlaying)
        {
            EnsureId();
        }
    }
#endif

    private void EnsureId()
    {
        if (string.IsNullOrEmpty(uniqueId))
        {
            uniqueId = System.Guid.NewGuid().ToString("N");
#if UNITY_EDITOR
            // Mark the object as dirty so the GUID gets saved
            if (!Application.isPlaying)
            {
                EditorUtility.SetDirty(this);
            }
#endif
        }
    }

#if UNITY_EDITOR
    [ContextMenu("Regenerate GUID (Editor Only)")]
    private void RegenerateGuid()
    {
        if (Application.isPlaying)
        {
            Debug.LogWarning("Cannot regenerate GUID during play mode");
            return;
        }

        uniqueId = System.Guid.NewGuid().ToString("N");
        EditorUtility.SetDirty(this);
        Debug.Log($"Regenerated GUID for {gameObject.name}: {uniqueId}");
    }
#endif
}



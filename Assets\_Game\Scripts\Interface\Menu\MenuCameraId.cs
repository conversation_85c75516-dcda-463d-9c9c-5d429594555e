using UnityEngine;

[ExecuteAlways]
public class MenuCameraId : MonoBehaviour
{
    [SerializeField]
    private string uniqueId = "";

    public string UniqueId => uniqueId;

    private void Awake()
    {
        EnsureId();
    }

    private void OnValidate()
    {
        EnsureId();
    }

    private void EnsureId()
    {
        if (string.IsNullOrEmpty(uniqueId))
        {
            uniqueId = System.Guid.NewGuid().ToString("N");
        }
    }
}



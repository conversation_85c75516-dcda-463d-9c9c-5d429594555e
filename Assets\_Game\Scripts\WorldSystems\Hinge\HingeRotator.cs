using UnityEngine;
using System.Collections;
using System;
using AudioSystem;

/// <summary>
/// Interactable hinge component for lids, chests, trapdoors, small doors.
/// - No Animator, no physics, no tween libs. Pure transform rotation.
/// - Works with existing InteractionManager via IInteractable.
/// - Optional audio through AudioSystems.
///
/// Attach this script to the moving piece (the mesh you want to rotate).
///
/// Pivot options:
/// - Use the object's own pivot (default), or
/// - Create a runtime pivot parent at a custom local position.
///
/// Rotation options:
/// - Set closed/open angles (degrees) around a chosen local axis.
/// - Drive motion by fixed duration or angular speed.
/// - Built-in ease presets or custom curve.
/// </summary>
[DisallowMultipleComponent]
public class HingeRotator : BaseInteractable, IHoldInteractable
{
    // --- Setup ---
    public enum AuthoringMode { AutomaticFromSavedPoses, Manual }
    public enum PivotMode { UseTransformPivot, UseCustomPivotParent }
    public enum HingeAxis { X, Y, Z }
    public enum EasingType { Linear, EaseIn, EaseOut, EaseInOut, Custom }

    [Header("Hinge Setup")]
    [Tooltip("Choose simplified automatic authoring from saved poses, or manual setup.")]
    [SerializeField] private AuthoringMode authoringMode = AuthoringMode.AutomaticFromSavedPoses;

    [Tooltip("Object to rotate. Defaults to this transform.")]
    [SerializeField] private Transform movingPart;

    [Tooltip("Pivot strategy: use current transform pivot, or create a runtime parent at a custom local position.")]
    [SerializeField] private PivotMode pivotMode = PivotMode.UseTransformPivot;

    [Tooltip("Optional transform whose position will be used as the pivot when creating a custom pivot parent.")]
    [SerializeField] private Transform pivotMarker;

    [Tooltip("Local position (relative to movingPart) where the hinge pivot should be created.")]
    [SerializeField] private Vector3 customPivotLocalPosition = Vector3.zero;

    [Tooltip("Create the custom pivot parent on Awake if PivotMode=UseCustomPivotParent")]
    [SerializeField] private bool autoCreatePivotAtAwake = true;

    [Header("Rotation")]
    [Tooltip("Local axis to rotate around.")]
    [SerializeField] private HingeAxis hingeAxis = HingeAxis.Y;

    [Tooltip("Invert the axis direction (flips sign of angles)")]
    [SerializeField] private bool invertAxis = false;

    [Tooltip("Closed angle offset (deg) relative to initial local rotation")]
    [SerializeField] private float closedAngle = 0f;

    [Tooltip("Open angle offset (deg) relative to initial local rotation")]
    [SerializeField] private float openAngle = 90f;

    [Tooltip("Start opened on scene load")]
    [SerializeField] private bool startOpen = false;

    [Header("Motion")]
    [Tooltip("If true, use duration for motion. If false, use angular speed (deg/s).")]
    [SerializeField] private bool useDuration = true;

    [Tooltip("Seconds to fully open")]
    [SerializeField] private float openDuration = 0.6f;

    [Tooltip("Seconds to fully close")]
    [SerializeField] private float closeDuration = 0.6f;

    [Tooltip("Angular speed (deg/s) if not using duration")]
    [SerializeField] private float angularSpeed = 120f;

    [Tooltip("Easing applied to 0..1 motion t")]
    [SerializeField] private EasingType easing = EasingType.EaseInOut;

    [Tooltip("Custom curve for EasingType.Custom. X=progress, Y=output.")]
    [SerializeField] private AnimationCurve customEaseCurve = AnimationCurve.Linear(0, 0, 1, 1);

    [Tooltip("Use unscaled time for motion")]
    [SerializeField] private bool useUnscaledTime = false;

    [Header("Interaction")]
    [Tooltip("Optional name shown in prompts (e.g., Open Lid / Close Lid)")]
    [SerializeField] private string displayName = "Lid";

    [Tooltip("Prevent opening. Plays locked sound if assigned.")]
    [SerializeField] private bool isLocked = false;

    [Header("Audio")] 
    [Tooltip("Preferred: raise events through this channel. If null, falls back to GlobalAudioManager.")]
    [SerializeField] private AudioEventChannel audioChannel;

    [SerializeField] private AudioEventDefinition openStartSound;
    [SerializeField] private AudioEventDefinition openEndSound;
    [SerializeField] private AudioEventDefinition closeStartSound;
    [SerializeField] private AudioEventDefinition closeEndSound;
    [SerializeField] private AudioEventDefinition lockedSound;
    [Tooltip("Optional loop while the hinge is moving")]
    [SerializeField] private AudioEventDefinition movingLoopSound;
    [SerializeField] private float movingLoopFadeOutTime = 0.2f;

    // --- Runtime ---
    private Transform rotationRoot;            // The transform we actually rotate (movingPart or runtime pivot parent)
    [SerializeField] private Transform runtimePivotParent;      // Created if PivotMode=UseCustomPivotParent (serialized so it persists)
    private Quaternion initialLocalRotation;   // Baseline local rotation for angle offsets
    private Vector3 axisVectorLocal;           // Cached axis vector in local space
    [SerializeField, HideInInspector] private Quaternion baselineChildLocalRotation; // Child local rot snapshot at init
    [SerializeField, HideInInspector] private Vector3 baselineChildLocalPosition;   // Child local pos snapshot at init
    [SerializeField, HideInInspector] private Vector3 baselineParentLocalPosition;  // Pivot/root local pos snapshot at init

    private bool isOpen;
    private bool isMoving;
    private bool targetOpen;
    private float fromAngle;
    private float toAngle;
    private float moveDuration;
    private float moveElapsed;
    private float currentAngle;                // Current angle offset from initialLocalRotation along axis

    private string movingLoopId;               // Unique id to control loop stop via GlobalAudioManager
    


    // Saved authoring poses (need to exist in builds too because runtime animation uses them)
    [SerializeField, HideInInspector] private Vector3 authorClosedWorldPos;
    [SerializeField, HideInInspector] private Quaternion authorClosedWorldRot;
    [SerializeField, HideInInspector] private bool authorHasClosed;
    [SerializeField, HideInInspector] private Vector3 authorOpenWorldPos;
    [SerializeField, HideInInspector] private Quaternion authorOpenWorldRot;
    [SerializeField, HideInInspector] private bool authorHasOpen;
    
    // Solved hinge data stored in PARENT-LOCAL SPACE for persistence
    [SerializeField, HideInInspector] private bool solvedHasData;
    [SerializeField, HideInInspector] private Vector3 solvedPivotLocal;  // Local to parent
    [SerializeField, HideInInspector] private Vector3 solvedAxisLocal;   // Local to parent
    [SerializeField, HideInInspector] private float solvedAngleDeg;
    // If true, solved pivot/axis are stored in world space (used when there is no parent)
    [SerializeField, HideInInspector] private bool solvedInWorldSpace;

#if UNITY_EDITOR
    // Editor-only manual pivot authoring state
    [SerializeField, HideInInspector] private bool editorPivotEditMode;
    [SerializeField, HideInInspector] private Vector3 editorPivotWorldPos;
#endif

    // --- Unity ---
    protected override void Awake()
    {
        base.Awake();

        if (movingPart == null)
            movingPart = transform;

        axisVectorLocal = GetAxisVector();
        rotationRoot = movingPart;

        // Automatic authoring: if we have solved data, create the runtime pivot parent
        if (authoringMode == AuthoringMode.AutomaticFromSavedPoses && solvedHasData)
        {
            // If a pivot was already created during authoring, don't create a duplicate at runtime
            if (runtimePivotParent == null)
            {
                CreateRuntimePivotFromSolvedData();
            }
        }
        // Manual mode with custom pivot
        else if (authoringMode == AuthoringMode.Manual && pivotMode == PivotMode.UseCustomPivotParent && autoCreatePivotAtAwake)
        {
            if (runtimePivotParent == null)
                CreateRuntimePivotParent();
        }

        // Fallback: if a pivot parent exists in hierarchy but reference was lost, restore it
        if (pivotMode == PivotMode.UseCustomPivotParent && runtimePivotParent == null && movingPart != null)
        {
            Transform parent = movingPart.parent;
            if (parent != null && parent != transform && parent.name.EndsWith("_HingePivot", StringComparison.Ordinal))
            {
                runtimePivotParent = parent;
            }
        }

        // Set the rotation root
        if (runtimePivotParent != null)
        {
            rotationRoot = runtimePivotParent;
        }
        else
        {
            rotationRoot = movingPart;
        }

        initialLocalRotation = rotationRoot.localRotation;
        Debug.Log($"[HingeRotator] Awake: Captured TRUE initialLocalRotation = {initialLocalRotation.eulerAngles}");
        // Snapshot baselines used to absorb restored poses (world-pos preserving)
        if (movingPart != null)
        {
            baselineChildLocalRotation = movingPart.localRotation;
            baselineChildLocalPosition = movingPart.localPosition;
        }
        baselineParentLocalPosition = rotationRoot.localPosition;
        movingLoopId = $"Hinge_{GetInstanceID()}_moving";

        // Initialize state - but DON'T apply angle yet if we have HeavyObjectManager
        isOpen = startOpen;
        targetOpen = startOpen;
        currentAngle = isOpen ? openAngle : closedAngle;
        
        // Only apply angle if no HeavyObjectManager (will be detected later in calibration)
        var heavyManager = GetComponent<HeavyObjectManager>();
        if (heavyManager == null)
        {
            ApplyAngle(currentAngle);
            Debug.Log($"[HingeRotator] Awake: Applied initial angle {currentAngle}° (no HeavyObjectManager)");
        }
        else
        {
            Debug.Log($"[HingeRotator] Awake: Skipped applying angle - HeavyObjectManager will restore");
        }
    }

    protected override void Start()
    {
        base.Start();
        // Delay calibration to ensure HeavyObjectManager has restored first
        StartCoroutine(DelayedCalibration());
    }
    
    private IEnumerator DelayedCalibration()
    {
        // Wait a frame for HeavyObjectManager to restore
        yield return null;
        CalibrateStateFromCurrentAngle();
    }

    /// <summary>
    /// Detect if we're actually open or closed based on current rotation
    /// </summary>
    private void CalibrateStateFromCurrentAngle()
    {
        if (rotationRoot == null || movingPart == null)
        {
            Debug.Log("[HingeRotator] CalibrateState: missing refs");
            return;
        }

        float midpoint = (openAngle + closedAngle) / 2f;

        // Compute parent-local transform that matches the restored child world transform
        Quaternion desiredParentLocalRot;
        Vector3 desiredParentLocalPos;
        GetDesiredParentLocalFromChildWorld(out desiredParentLocalRot, out desiredParentLocalPos);

        // Compute actual signed angle along hinge axis from initialLocalRotation → desiredParentLocalRot
        float actualAngle = ComputeSignedAngleFromParentLocal(desiredParentLocalRot);
        float minA = Mathf.Min(closedAngle, openAngle);
        float maxA = Mathf.Max(closedAngle, openAngle);
        float clampedAngle = Mathf.Clamp(actualAngle, minA, maxA);

        bool shouldBeOpen = (closedAngle < openAngle) ? (clampedAngle > midpoint) : (clampedAngle < midpoint);

        // Update flags/state first
        isOpen = shouldBeOpen;
        targetOpen = shouldBeOpen;
        currentAngle = clampedAngle;

        // Resync transforms so hinge parent expresses the restored pose, and child returns to baseline
        if (rotationRoot != movingPart)
        {
            if (shouldBeOpen)
            {
                rotationRoot.localRotation = desiredParentLocalRot;
                rotationRoot.localPosition = desiredParentLocalPos;
                movingPart.localRotation = baselineChildLocalRotation;
                movingPart.localPosition = baselineChildLocalPosition;
            }
            else
            {
                Quaternion closedLocal = initialLocalRotation * Quaternion.AngleAxis(invertAxis ? -closedAngle : closedAngle, axisVectorLocal);
                rotationRoot.localRotation = closedLocal;
                rotationRoot.localPosition = baselineParentLocalPosition;
                movingPart.localRotation = baselineChildLocalRotation;
                movingPart.localPosition = baselineChildLocalPosition;
                currentAngle = closedAngle;
            }
        }
        else
        {
            // No custom pivot. Only snap if we think we're closed.
            if (!shouldBeOpen)
            {
                ApplyAngle(closedAngle);
                currentAngle = closedAngle;
            }
        }

        Debug.Log($"[HingeRotator] CalibrateState: actual={actualAngle:F1}° -> clamped={clampedAngle:F1}°, isOpen={isOpen}");
    }



        private void Update()
    {
        if (!isMoving) return;

        float dt = useUnscaledTime ? Time.unscaledDeltaTime : Time.deltaTime;
        moveElapsed += dt;
        float t = Mathf.Clamp01(moveDuration > 0.0001f ? moveElapsed / moveDuration : 1f);
        float eased = Ease(t);

        float angle = Mathf.Lerp(fromAngle, toAngle, eased);
        ApplyAngle(angle);

        if (t >= 1f)
        {
            isMoving = false;
            // isOpen already set in BeginMove, don't override it here
            currentAngle = toAngle;
            ApplyAngle(currentAngle);
            StopMovingLoop();
            PlayEndSound(isOpen);
        }
    }

    // --- IInteractable ---
    public override void Interact(GameObject interactor)
    {
        if (isLocked)
        {
            PlayOneShot(lockedSound);
            return;
        }

        // Toggle target; allow reversing mid-motion
        bool wantOpen = !targetOpen;  // Use targetOpen instead of isOpen for immediate responsiveness
        Debug.Log($"[HingeRotator] Interact: isOpen={isOpen}, targetOpen={targetOpen}, wantOpen={wantOpen}");
        BeginMove(wantOpen);
    }

    public override string GetInteractionPrompt()
    {
        if (isLocked) return $"Locked {displayName}";
        string verb = (isOpen ? "Close" : "Open");
        return string.IsNullOrWhiteSpace(displayName)
            ? verb
            : $"{verb} {displayName} (Hold F + mouse)";
    }

    public override bool CanInteract(GameObject interactor)
    {
        // Allow interaction anytime; Interact toggles and will reverse mid-motion smoothly
        return true;
    }

    // --- Control API ---
    public void Open()
    {
        if (isLocked) { PlayOneShot(lockedSound); return; }
        BeginMove(true);
    }

    public void Close()
    {
        BeginMove(false);
    }

    public void SetLocked(bool locked)
    {
        isLocked = locked;
    }

    // Expose current open/closed state for persistence systems
    public bool IsOpen => isOpen;

    // Instantly set open/closed without sounds or tween (used by persistence restore)
    public void SnapSetOpen(bool open)
    {
        // Ensure rotation root exists
        if (rotationRoot == null)
        {
            rotationRoot = runtimePivotParent != null ? runtimePivotParent : (movingPart != null ? movingPart : transform);
        }
        // Don't override initialLocalRotation - it should be set in Awake()
        Debug.Log($"[HingeRotator] SnapSetOpen: Using existing initialLocalRotation = {initialLocalRotation.eulerAngles}");

        isOpen = open;
        targetOpen = open;
        isMoving = false;
        currentAngle = open ? openAngle : closedAngle;
        ApplyAngle(currentAngle);
        startOpen = open;
    }

    // --- Internal ---
    private void BeginMove(bool open)
    {
        targetOpen = open;
        fromAngle = currentAngle;
        toAngle = open ? openAngle : closedAngle;

        if (Mathf.Approximately(fromAngle, toAngle))
        {
            // Nothing to do
            PlayEndSound(open);
            return;
        }

        moveDuration = ComputeDuration(Mathf.Abs(toAngle - fromAngle), open);
        moveElapsed = 0f;
        isMoving = true;

        // Update state immediately for persistence - don't wait for animation to finish
        isOpen = open;

        PlayStartSound(open);
        StartMovingLoop();
    }

    private float ComputeDuration(float angleDelta, bool open)
    {
        if (useDuration)
        {
            float fullDuration = open ? openDuration : closeDuration;
            float fullAngleDelta = Mathf.Abs(openAngle - closedAngle);
            // Scale duration based on how much we're actually moving
            return fullDuration * (angleDelta / Mathf.Max(0.001f, fullAngleDelta));
        }
        else
        {
            float speed = Mathf.Max(0.0001f, angularSpeed);
            return angleDelta / speed;
        }
    }

    private void ApplyAngle(float angle)
    {
        currentAngle = angle;
        Quaternion offset = Quaternion.AngleAxis(invertAxis ? -angle : angle, axisVectorLocal);
        rotationRoot.localRotation = initialLocalRotation * offset;
    }

    // --- Hold interaction (F-hold + mouse drag) ---
    [Header("Hold Control")]
    [SerializeField, Tooltip("Enable F-hold + mouse left/right to scrub the hinge angle")]
    private bool enableHoldControl = true;
    [SerializeField, Tooltip("Degrees per mouse X unit (scaled by Time.deltaTime)")]
    private float holdSensitivity = 300f;
    [SerializeField, Tooltip("While holding, clamp between closed/open and optionally snap on release")]
    private bool snapToEndsOnRelease = false;
    [SerializeField, Tooltip("Snap threshold as fraction of travel (0..1)")]
    private float snapThreshold = 0.1f;

    private bool isHoldActive;
    private float holdStartAngle;

    public bool WantsHold(GameObject interactor)
    {
        if (!enableHoldControl) return false;
        if (isLocked) return false;
        return true;
    }

    public void OnHoldStarted(GameObject interactor)
    {
        isHoldActive = true;
        holdStartAngle = currentAngle;
        StopMovingLoop();
        isMoving = false;
        // Do not change isOpen/state yet; we'll update as scrubbing happens
        PlayStartSound(true); // small cue; optional
    }

    public void OnHold(GameObject interactor, float mouseDeltaX, float mouseDeltaY)
    {
        if (!isHoldActive) return;
        // Convert horizontal mouse movement into angle delta (inverted for expected feel)
        // Respect invertAxis so the hold direction also flips with axis inversion
        float deltaDeg = -mouseDeltaX * holdSensitivity * (useUnscaledTime ? Time.unscaledDeltaTime : Time.deltaTime) * (invertAxis ? -1f : 1f);

        float minA = Mathf.Min(closedAngle, openAngle);
        float maxA = Mathf.Max(closedAngle, openAngle);

        float next = Mathf.Clamp(currentAngle + deltaDeg, minA, maxA);
        ApplyAngle(next);
        currentAngle = next;

        // Update logical open/close based on midpoint
        float midpoint = (openAngle + closedAngle) * 0.5f;
        bool nowOpen = (closedAngle < openAngle) ? (currentAngle > midpoint) : (currentAngle < midpoint);
        if (nowOpen != isOpen)
        {
            isOpen = nowOpen;
        }
        targetOpen = isOpen;
    }

    public void OnHoldEnded(GameObject interactor, bool canceled)
    {
        if (!isHoldActive) return;
        isHoldActive = false;

        if (snapToEndsOnRelease)
        {
            float minA = Mathf.Min(closedAngle, openAngle);
            float maxA = Mathf.Max(closedAngle, openAngle);
            float range = Mathf.Max(0.0001f, maxA - minA);
            float t = Mathf.InverseLerp(minA, maxA, currentAngle);
            float snapMin = snapThreshold;
            float snapMax = 1f - snapThreshold;
            float targetA = currentAngle;
            if (t <= snapMin) targetA = closedAngle;
            else if (t >= snapMax) targetA = openAngle;

            // Animate a small settle if not already at target
            fromAngle = currentAngle;
            toAngle = targetA;
            moveDuration = ComputeDuration(Mathf.Abs(toAngle - fromAngle), toAngle == openAngle);
            moveElapsed = 0f;
            isMoving = !Mathf.Approximately(fromAngle, toAngle);
            isOpen = Mathf.Approximately(targetA, openAngle);
            targetOpen = isOpen;
            if (isMoving)
            {
                StartMovingLoop();
            }
            else
            {
                PlayEndSound(isOpen);
            }
        }
        else
        {
            // Just play an end cue based on current side
            PlayEndSound(isOpen);
        }
    }

            private float ComputeSignedAngleFromInitial()
    {
        return ComputeSignedAngleFromParentLocal(rotationRoot != null ? rotationRoot.localRotation : Quaternion.identity);
    }

    private float ComputeSignedAngleFromParentLocal(Quaternion parentLocalRotation)
    {
        Quaternion delta = Quaternion.Inverse(initialLocalRotation) * parentLocalRotation;

        Vector3 axis = axisVectorLocal.normalized;
        Vector3 basis = Vector3.Cross(axis, Vector3.right);
        if (basis.sqrMagnitude < 1e-4f) basis = Vector3.Cross(axis, Vector3.forward);
        basis.Normalize();

        Vector3 rotated = delta * basis;
        float signed = Vector3.SignedAngle(basis, rotated, axis);
        float result = invertAxis ? -signed : signed;
        return result;
    }

    private void GetDesiredParentLocalFromChildWorld(out Quaternion parentLocalRotation, out Vector3 parentLocalPosition)
    {
        if (rotationRoot == null || movingPart == null)
        {
            parentLocalRotation = initialLocalRotation;
            parentLocalPosition = baselineParentLocalPosition;
            return;
        }

        if (rotationRoot == movingPart)
        {
            parentLocalRotation = rotationRoot.localRotation;
            parentLocalPosition = rotationRoot.localPosition;
            return;
        }

        // Desired parent world transform that preserves the child's restored world pose
        Quaternion desiredParentWorldRot = movingPart.rotation * Quaternion.Inverse(baselineChildLocalRotation);
        Vector3 desiredParentWorldPos = movingPart.position - (desiredParentWorldRot * baselineChildLocalPosition);

        Transform parentOfRoot = rotationRoot.parent;
        if (parentOfRoot != null)
        {
            parentLocalRotation = Quaternion.Inverse(parentOfRoot.rotation) * desiredParentWorldRot;
            parentLocalPosition = Quaternion.Inverse(parentOfRoot.rotation) * (desiredParentWorldPos - parentOfRoot.position);
        }
        else
        {
            parentLocalRotation = desiredParentWorldRot;
            parentLocalPosition = desiredParentWorldPos;
        }
    }

    private Vector3 GetAxisVector()
    {
        switch (hingeAxis)
        {
            case HingeAxis.X: return Vector3.right;
            case HingeAxis.Y: return Vector3.up;
            case HingeAxis.Z: return Vector3.forward;
            default: return Vector3.up;
        }
    }

    private float Ease(float t)
    {
        switch (easing)
        {
            case EasingType.Linear: return t;
            case EasingType.EaseIn: return t * t;
            case EasingType.EaseOut: return 1f - Mathf.Pow(1f - t, 2f);
            case EasingType.EaseInOut: return t < 0.5f ? 2f * t * t : 1f - Mathf.Pow(-2f * t + 2f, 2f) / 2f;
            case EasingType.Custom: return customEaseCurve != null ? Mathf.Clamp01(customEaseCurve.Evaluate(t)) : t;
            default: return t;
        }
    }

    private void CreateRuntimePivotParent()
    {
        if (movingPart == null) return;

        // Create a new parent at the desired pivot position in world space
        Vector3 pivotWorldPos = pivotMarker != null 
            ? pivotMarker.position 
            : movingPart.TransformPoint(customPivotLocalPosition);
        Transform originalParent = movingPart.parent;

        GameObject pivotGo = new GameObject(movingPart.name + "_HingePivot");
        runtimePivotParent = pivotGo.transform;
        runtimePivotParent.SetParent(originalParent, worldPositionStays: true);
        runtimePivotParent.position = pivotWorldPos;
        runtimePivotParent.rotation = movingPart.rotation; // keep orientation

        movingPart.SetParent(runtimePivotParent, worldPositionStays: true);

        rotationRoot = runtimePivotParent;
    }

    private void CreateRuntimePivotFromSolvedData()
    {
        if (movingPart == null || !solvedHasData) return;

        // First, ensure movingPart is at the closed pose
        movingPart.SetPositionAndRotation(authorClosedWorldPos, authorClosedWorldRot);

        Transform originalParent = movingPart.parent;

        // Create pivot GO at the solved world position
        GameObject pivotGo = new GameObject(movingPart.name + "_HingePivot");
        runtimePivotParent = pivotGo.transform;

        // Resolve world-space pivot/axis depending on how we stored it
        Vector3 pivotWorldPos;
        Vector3 axisWorldDir;
        if (solvedInWorldSpace || originalParent == null)
        {
            pivotWorldPos = solvedPivotLocal;
            axisWorldDir = solvedAxisLocal.normalized;
        }
        else
        {
            pivotWorldPos = originalParent.TransformPoint(solvedPivotLocal);
            axisWorldDir = originalParent.TransformDirection(solvedAxisLocal).normalized;
        }

        runtimePivotParent.position = pivotWorldPos;

        // Set the pivot parent under the original parent AFTER setting position (if any)
        if (originalParent != null)
        {
            runtimePivotParent.SetParent(originalParent, worldPositionStays: true);
        }

        // Orient the pivot so its Y axis aligns with the solved axis
        var refPerp = Vector3.Cross(axisWorldDir, Vector3.up);
        if (refPerp.sqrMagnitude > 1e-6f)
        {
            runtimePivotParent.rotation = Quaternion.LookRotation(refPerp.normalized, axisWorldDir);
        }
        else
        {
            var altPerp = Vector3.Cross(axisWorldDir, Vector3.forward);
            runtimePivotParent.rotation = Quaternion.LookRotation(altPerp.normalized, axisWorldDir);
        }

        // Parent the movingPart to the pivot, maintaining its world position
        movingPart.SetParent(runtimePivotParent, worldPositionStays: true);

        // Store the pivot's current local rotation as the initial rotation
        initialLocalRotation = runtimePivotParent.localRotation;

        // Set up the angles
        closedAngle = 0f;
        openAngle = solvedAngleDeg;

        // Configure to use this pivot
        pivotMode = PivotMode.UseCustomPivotParent;
        hingeAxis = HingeAxis.Y; // We aligned it to Y
        invertAxis = false;
        axisVectorLocal = Vector3.up;

        rotationRoot = runtimePivotParent;
    }

    // --- Audio ---
    private void PlayStartSound(bool opening)
    {
        PlayOneShot(opening ? openStartSound : closeStartSound);
    }

    private void PlayEndSound(bool opened)
    {
        PlayOneShot(opened ? openEndSound : closeEndSound);
    }

    private void PlayOneShot(AudioEventDefinition def)
    {
        if (def == null) return;

        if (audioChannel != null)
        {
            var data = def.eventData;
            audioChannel.RaisePositionalEvent(data, transform.position);
        }
        else
        {
            GlobalAudioManager.Instance?.PlayAudioEvent(def, transform.position);
        }
    }

    private void StartMovingLoop()
    {
        if (movingLoopSound == null) return;

        // Looping control requires a unique event name; use channel path for that
        AudioEventData dataCopy = CreateEventDataCopy(movingLoopSound.eventData);
        dataCopy.eventName = movingLoopId;

        var parameters = new AudioPlaybackParams
        {
            loop = true,
            position = transform.position
        };

        if (audioChannel != null)
        {
            audioChannel.RaiseParameterizedEvent(dataCopy, parameters);
        }
        else
        {
            // Fallback: still play, but cannot reliably stop/fade the loop without a unique id in GlobalAudioManager
            GlobalAudioManager.Instance?.PlayAudioEvent(movingLoopSound, transform.position, new AudioPlaybackParams { loop = true });
        }
    }

    private void StopMovingLoop()
    {
        if (string.IsNullOrEmpty(movingLoopId)) return;
        GlobalAudioManager.Instance?.FadeOutLoopingSound(movingLoopId, movingLoopFadeOutTime);
    }

    private static AudioEventData CreateEventDataCopy(AudioEventData original)
    {
        var copy = new AudioEventData
        {
            eventName = original.eventName,
            clips = original.clips,
            baseVolume = original.baseVolume,
            basePitch = original.basePitch,
            volumeVariation = original.volumeVariation,
            pitchVariation = original.pitchVariation,
            mixerGroup = original.mixerGroup,
            is3D = original.is3D,
            minDistance = original.minDistance,
            maxDistance = original.maxDistance
        };
        return copy;
    }

    // --- Math helpers and hinge solving (available at runtime and editor) ---
    private bool TryComputeHingeFromClosedOpen(
        Vector3 closedPos, Quaternion closedRot,
        Vector3 openPos, Quaternion openRot,
        out Vector3 pivotWorld, out Vector3 axisWorld, out float angleDeg)
    {
        // Relative rotation R and translation t mapping x -> x' : x' = R x + t
        Quaternion deltaRot = openRot * Quaternion.Inverse(closedRot);

        // Extract axis/angle
        deltaRot.ToAngleAxis(out float rawAngleDeg, out Vector3 rawAxis);
        if (rawAxis.sqrMagnitude < 1e-8f)
        {
            pivotWorld = Vector3.zero; axisWorld = Vector3.up; angleDeg = 0f; return false;
        }
        if (rawAngleDeg > 180f)
        {
            rawAngleDeg = 360f - rawAngleDeg;
            rawAxis = -rawAxis;
        }

        // Reject tiny rotations
        if (Mathf.Abs(rawAngleDeg) < 0.01f)
        {
            pivotWorld = closedPos; axisWorld = rawAxis; angleDeg = 0f; return false;
        }

        // Build 3x3 rotation matrix
        Mat3 R = Mat3.FromQuaternion(deltaRot);
        Vector3 t = openPos - R.MultiplyPoint3x3(closedPos);

        // Solve (I - R) p = t  => p = (I - R)^{-1} t
        Mat3 IminusR = Mat3.Identity() - R;
        if (!IminusR.TryInverse(out Mat3 inv))
        {
            // Singular (near 180°). Use midpoint as a valid point on the axis line
            pivotWorld = 0.5f * (closedPos + openPos);
            axisWorld = rawAxis.normalized;
            angleDeg = rawAngleDeg;
            return true;
        }

        Vector3 pWorld = inv.MultiplyVector(t);
        pivotWorld = pWorld; // Direct solution in world coordinates
        axisWorld = rawAxis.normalized;
        angleDeg = rawAngleDeg;
        return true;
    }

    // Minimal 3x3 matrix helper
    private struct Mat3
    {
        public float m00, m01, m02;
        public float m10, m11, m12;
        public float m20, m21, m22;

        public static Mat3 Identity()
        {
            return new Mat3 { m00 = 1, m11 = 1, m22 = 1 };
        }

        public static Mat3 FromQuaternion(Quaternion q)
        {
            // Unity Quaternion to rotation matrix (right-handed)
            q.Normalize();
            float x = q.x, y = q.y, z = q.z, w = q.w;
            float xx = x * x, yy = y * y, zz = z * z;
            float xy = x * y, xz = x * z, yz = y * z;
            float wx = w * x, wy = w * y, wz = w * z;

            Mat3 r;
            r.m00 = 1f - 2f * (yy + zz);
            r.m01 = 2f * (xy - wz);
            r.m02 = 2f * (xz + wy);
            r.m10 = 2f * (xy + wz);
            r.m11 = 1f - 2f * (xx + zz);
            r.m12 = 2f * (yz - wx);
            r.m20 = 2f * (xz - wy);
            r.m21 = 2f * (yz + wx);
            r.m22 = 1f - 2f * (xx + yy);
            return r;
        }

        public static Mat3 operator -(Mat3 a, Mat3 b)
        {
            Mat3 r;
            r.m00 = a.m00 - b.m00; r.m01 = a.m01 - b.m01; r.m02 = a.m02 - b.m02;
            r.m10 = a.m10 - b.m10; r.m11 = a.m11 - b.m11; r.m12 = a.m12 - b.m12;
            r.m20 = a.m20 - b.m20; r.m21 = a.m21 - b.m21; r.m22 = a.m22 - b.m22;
            return r;
        }

        public bool TryInverse(out Mat3 inv)
        {
            float c00 =  (m11 * m22 - m12 * m21);
            float c01 = -(m10 * m22 - m12 * m20);
            float c02 =  (m10 * m21 - m11 * m20);
            float c10 = -(m01 * m22 - m02 * m21);
            float c11 =  (m00 * m22 - m02 * m20);
            float c12 = -(m00 * m21 - m01 * m20);
            float c20 =  (m01 * m12 - m02 * m11);
            float c21 = -(m00 * m12 - m02 * m10);
            float c22 =  (m00 * m11 - m01 * m10);

            float det = m00 * c00 + m01 * c01 + m02 * c02;
            if (Mathf.Abs(det) < 1e-6f)
            {
                inv = default; return false;
            }
            float invDet = 1f / det;

            inv.m00 = c00 * invDet; inv.m01 = c10 * invDet; inv.m02 = c20 * invDet;
            inv.m10 = c01 * invDet; inv.m11 = c11 * invDet; inv.m12 = c21 * invDet;
            inv.m20 = c02 * invDet; inv.m21 = c12 * invDet; inv.m22 = c22 * invDet;
            return true;
        }

        public Vector3 MultiplyVector(Vector3 v)
        {
            return new Vector3(
                m00 * v.x + m01 * v.y + m02 * v.z,
                m10 * v.x + m11 * v.y + m12 * v.z,
                m20 * v.x + m21 * v.y + m22 * v.z
            );
        }

        public Vector3 MultiplyPoint3x3(Vector3 p)
        {
            return MultiplyVector(p);
        }
    }


    // --- Editor helpers ---
#if UNITY_EDITOR
    // ----- Authoring: pose-based auto-detection -----
    // Poses are serialized above (outside editor-only) so they persist in builds too

    // Public editor-only accessors to avoid reflection/private access
    public Transform EditorRuntimePivotParent { get => runtimePivotParent; set => runtimePivotParent = value; }
    public PivotMode EditorPivotMode { get => pivotMode; set => pivotMode = value; }
    public AuthoringMode EditorAuthoringMode { get => authoringMode; set => authoringMode = value; }
    public Transform EditorRotationRoot { get => rotationRoot; set => rotationRoot = value; }
    public Quaternion EditorInitialLocalRotation { get => initialLocalRotation; set => initialLocalRotation = value; }
    public bool EditorStartOpen => startOpen;
    public float EditorOpenAngle => openAngle;
    public float EditorClosedAngle => closedAngle;
    public void EditorApplyAngle(float angle) { ApplyAngle(angle); }
    public bool EditorSolvedHasData { get => solvedHasData; set => solvedHasData = value; }
    public Transform EditorPivotMarker => pivotMarker;
    public Vector3 EditorCustomPivotLocalPosition => customPivotLocalPosition;
    public Transform EditorMovingPart => movingPart;

    [ContextMenu("Hinge Authoring/Save Closed Pose (Current)")] 
    private void Author_SaveClosedPose()
    {
        EnsureRefs();
        authorClosedWorldPos = movingPart.position;
        authorClosedWorldRot = movingPart.rotation;
        authorHasClosed = true;
        UnityEditor.EditorUtility.SetDirty(this);
    }

    [ContextMenu("Hinge Authoring/Save Open Pose (Current)")] 
    private void Author_SaveOpenPose()
    {
        EnsureRefs();
        authorOpenWorldPos = movingPart.position;
        authorOpenWorldRot = movingPart.rotation;
        authorHasOpen = true;
        UnityEditor.EditorUtility.SetDirty(this);
        // Auto-build when both poses are present to simplify workflow
        if (authorHasClosed)
        {
            Author_BuildFromSavedPoses();
        }
    }

    [ContextMenu("Hinge Authoring/Build From Saved Poses")] 
    private void Author_BuildFromSavedPoses()
    {
        EnsureRefs();
        if (!authorHasClosed || !authorHasOpen)
        {
            UnityEngine.Debug.LogWarning("[HingeRotator] Capture both Closed and Open poses first.");
            return;
        }

        if (!TryComputeHingeFromClosedOpen(authorClosedWorldPos, authorClosedWorldRot,
                                           authorOpenWorldPos, authorOpenWorldRot,
                                           out Vector3 pivotWorld, out Vector3 axisWorld,
                                           out float angleDeg))
        {
            UnityEngine.Debug.LogWarning("[HingeRotator] Could not compute a stable hinge from poses.");
            return;
        }

        Transform originalParent = movingPart.parent;

        // Store the solved data. If there's no parent, store in world space to avoid creating a container
        solvedHasData = true;
        if (originalParent == null)
        {
            solvedInWorldSpace = true;
            solvedPivotLocal = pivotWorld;                // world space
            solvedAxisLocal = axisWorld.normalized;      // world space
        }
        else
        {
            solvedInWorldSpace = false;
            solvedPivotLocal = originalParent.InverseTransformPoint(pivotWorld);
            solvedAxisLocal = originalParent.InverseTransformDirection(axisWorld).normalized;
        }
        solvedAngleDeg = angleDeg;

        // First, ensure movingPart is at the closed pose
        movingPart.SetPositionAndRotation(authorClosedWorldPos, authorClosedWorldRot);

        // Create or reuse pivot GO
        if (runtimePivotParent == null)
        {
            GameObject pivotGo = new GameObject(movingPart.name + "_HingePivot");
            runtimePivotParent = pivotGo.transform;
        }

        // If we have a parent, attach under it, else keep at root. Then place/orient in world.
        if (originalParent != null)
        {
            runtimePivotParent.SetParent(originalParent, worldPositionStays: true);
        }

        // Position the pivot at the computed hinge location
        runtimePivotParent.position = pivotWorld;

        // Align pivot so its Y axis matches the hinge axis
        var refPerp = Vector3.Cross(axisWorld, Vector3.up);
        if (refPerp.sqrMagnitude > 1e-6f)
        {
            runtimePivotParent.rotation = Quaternion.LookRotation(refPerp.normalized, axisWorld);
        }
        else
        {
            var altPerp = Vector3.Cross(axisWorld, Vector3.forward);
            runtimePivotParent.rotation = Quaternion.LookRotation(altPerp.normalized, axisWorld);
        }

        // Parent the moving part to the pivot, keeping its current world transform
        movingPart.SetParent(runtimePivotParent, worldPositionStays: true);
        
        // Store the pivot's local rotation as the baseline (this is angle 0)
        initialLocalRotation = runtimePivotParent.localRotation;
        
        // Configure the system
        rotationRoot = runtimePivotParent;
        pivotMode = PivotMode.UseCustomPivotParent;
        hingeAxis = HingeAxis.Y;  // We aligned it to Y
        invertAxis = false;
        closedAngle = 0f;
        openAngle = angleDeg;
        startOpen = false;
        currentAngle = closedAngle;
        axisVectorLocal = Vector3.up;  // Since we aligned to Y

        UnityEditor.EditorUtility.SetDirty(this);
        UnityEngine.Debug.Log($"[HingeRotator] Built hinge. Angle={angleDeg:F2}°");
        UnityEngine.Debug.Log($"[HingeRotator] Pivot (world): {pivotWorld}, (local): {solvedPivotLocal}");
        UnityEngine.Debug.Log($"[HingeRotator] Axis (world): {axisWorld}, (local): {solvedAxisLocal}");
    }

    // Public wrappers for custom editor buttons
    public void Editor_SaveClosedPose() { Author_SaveClosedPose(); }
    public void Editor_SaveOpenPose() { Author_SaveOpenPose(); }
    public void Editor_BuildFromSavedPoses() { Author_BuildFromSavedPoses(); }

    [ContextMenu("Hinge Authoring/Move To Closed Pose")]
    private void Author_MoveToClosed()
    {
        EnsureRefs();
        if (authorHasClosed)
        {
            movingPart.SetPositionAndRotation(authorClosedWorldPos, authorClosedWorldRot);
        }
    }

    [ContextMenu("Hinge Authoring/Move To Open Pose")]
    private void Author_MoveToOpen()
    {
        EnsureRefs();
        if (authorHasOpen)
        {
            movingPart.SetPositionAndRotation(authorOpenWorldPos, authorOpenWorldRot);
        }
    }

    [ContextMenu("Hinge Authoring/Clear Saved Poses")]
    private void Author_ClearSavedPoses()
    {
        authorHasClosed = false;
        authorHasOpen = false;
        UnityEditor.EditorUtility.SetDirty(this);
    }

    [ContextMenu("Hinge Authoring/Reset Hinge Setup")]
    private void Author_ResetHingeSetup()
    {
        EnsureRefs();
        if (runtimePivotParent != null)
        {
            Transform parentOfPivot = runtimePivotParent.parent;
            // Reparent moving part back to the original parent of the pivot
            movingPart.SetParent(parentOfPivot, worldPositionStays: true);
            UnityEditor.Undo.DestroyObjectImmediate(runtimePivotParent.gameObject);
            runtimePivotParent = null;
        }
        rotationRoot = movingPart;
        pivotMode = PivotMode.UseTransformPivot;
        solvedHasData = false;
        solvedPivotLocal = Vector3.zero;
        solvedAxisLocal = Vector3.up;
        solvedAngleDeg = 0f;
        // Re-evaluate initial for intuitive authoring
        initialLocalRotation = rotationRoot.localRotation;
        axisVectorLocal = GetAxisVector();
        UnityEditor.EditorUtility.SetDirty(this);
    }


    [ContextMenu("Hinge/Create Pivot Parent Now")]
    private void ContextCreatePivotNow()
    {
        if (pivotMode != PivotMode.UseCustomPivotParent)
            pivotMode = PivotMode.UseCustomPivotParent;

        if (runtimePivotParent == null)
        {
            CreateRuntimePivotParent();
            initialLocalRotation = rotationRoot.localRotation;
            ApplyAngle(startOpen ? openAngle : closedAngle);
        }
    }

    [ContextMenu("Hinge/Set Current As Closed Angle")] 
    private void ContextSetClosed()
    {
        EnsureRefs();
        closedAngle = ComputeSignedAngleFromInitial();
        if (!isOpen) currentAngle = closedAngle;
    }

    [ContextMenu("Hinge/Set Current As Open Angle")] 
    private void ContextSetOpen()
    {
        EnsureRefs();
        openAngle = ComputeSignedAngleFromInitial();
        if (isOpen) currentAngle = openAngle;
    }

    private void OnValidate()
    {
        if (movingPart == null) movingPart = transform;
        axisVectorLocal = GetAxisVector();

        openDuration = Mathf.Max(0f, openDuration);
        closeDuration = Mathf.Max(0f, closeDuration);
        angularSpeed = Mathf.Max(0f, angularSpeed);

        // Keep preview in-editor
        if (!Application.isPlaying)
        {
            // Auto-solve when requested and both poses are available
            #if UNITY_EDITOR
            if (authoringMode == AuthoringMode.AutomaticFromSavedPoses && authorHasClosed && authorHasOpen && !solvedHasData)
            {
                Author_BuildFromSavedPoses();
            }
            #endif

            // Choose rotation root
            rotationRoot = (runtimePivotParent != null) ? runtimePivotParent : movingPart;
            
            if (rotationRoot != null)
            {
                // Re-evaluate initial based on current localRotation for intuitive authoring
                initialLocalRotation = rotationRoot.localRotation;
                ApplyAngle(startOpen ? openAngle : closedAngle);
            }
        }
    }

    private void OnDrawGizmosSelected()
    {
        EnsureRefs();
        Vector3 pivot;
        Vector3 axisWorld;
        
        if (runtimePivotParent != null)
        {
            pivot = runtimePivotParent.position;
            axisWorld = runtimePivotParent.TransformDirection(axisVectorLocal * (invertAxis ? -1f : 1f));
        }
        else if (solvedHasData && movingPart != null && movingPart.parent != null)
        {
            // Convert parent-local to world for gizmo display
            Transform parent = movingPart.parent;
            pivot = parent.TransformPoint(solvedPivotLocal);
            axisWorld = parent.TransformDirection(solvedAxisLocal).normalized * (invertAxis ? -1f : 1f);
        }
        else
        {
            Transform root = rotationRoot != null ? rotationRoot : transform;
            pivot = root.position;
            axisWorld = root.TransformDirection(axisVectorLocal * (invertAxis ? -1f : 1f));
        }

        Gizmos.color = new Color(1f, 0.6f, 0f, 0.9f);
        Gizmos.DrawLine(pivot, pivot + axisWorld * 0.5f);
        Gizmos.DrawSphere(pivot, 0.02f);
    }

    private void EnsureRefs()
    {
        if (movingPart == null) movingPart = transform;
        if (rotationRoot == null) rotationRoot = movingPart;
        axisVectorLocal = GetAxisVector();
        if (initialLocalRotation == default) initialLocalRotation = rotationRoot.localRotation;
    }
#endif
}
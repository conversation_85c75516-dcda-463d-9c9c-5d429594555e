using System;
using System.Collections.Generic;
using UnityEngine;
using KinematicCharacterController;

public class KinematicTrain : KinematicPlatform, IMoverController
{
    [Serializable]
    public class TrackPoint
    {
        public Vector3 position;
        public float waitTime = 0f;
        public float speedLimit = 1000f;
        [Tooltip("Roll (bank) around forward axis in degrees at this point")] public float rollDegrees = 0f;
        [Tooltip("Optional scene object to source position from")] public GameObject referenceObject;

        [Header("Bezier Handles (relative to point)")]
        public bool useCustomHandles = false;
        public Vector3 handleIn = new Vector3(-1f, 0f, 0f);
        public Vector3 handleOut = new Vector3(1f, 0f, 0f);

        public void UpdateFromReference()
        {
            if (referenceObject != null)
            {
                position = referenceObject.transform.position;
            }
        }
    }

    public enum PathMode
    {
        Linear,
        CatmullRom,
        Bezier
    }

    [Header("Track")]
    [SerializeField] private List<TrackPoint> track = new List<TrackPoint>();
    [SerializeField] private bool loopTrack = true;
    [SerializeField] private PathMode pathMode = PathMode.CatmullRom;
    [SerializeField] private bool autoBezierHandles = true;
    [SerializeField, Range(0f, 1f)] private float bezierTension = 0.5f;

    [Header("Movement")]
    [SerializeField] private float baseSpeed = 8f;
    [SerializeField] private float maxSpeed = 20f;
    [SerializeField] private float acceleration = 10f;
    [SerializeField] private float deceleration = 10f;

    [Header("Orientation")]
    [SerializeField] private bool alignToTangent = true;
    [SerializeField] private Vector3 worldUp = Vector3.up;
    [SerializeField] private bool bankFromCurvature = true;
    [SerializeField] private float bankingMultiplier = 1f;
    [SerializeField] private float bankingMaxDegrees = 12f;
    [SerializeField] private float bankSmoothing = 12f;

    [Header("Cars (Optional)")]
    [SerializeField] private List<Transform> cars = new List<Transform>();
    [SerializeField] private float carSpacingMeters = 2.5f;

    // Runtime state (kept independent of base implementation)
    private bool _moving = false;
    private bool _waiting = false;
    private float _waitCounter = 0f;
    private int _direction = 1; // 1 forward, -1 backward
    private int _segmentIndex = 0; // segment from point i to next(i)
    private float _segmentLength = 1f;
    private float _distanceOnSegment = 0f;
    private float _currentSpeed = 0f;
    private float _smoothedBankDeg = 0f;

    // Cached geometry
    private readonly List<float> _segmentLengths = new List<float>();
    private readonly List<float> _cumulativeLengths = new List<float>();
    private float _totalLength = 0f;

    // Sampling quality
    private const int SAMPLES_PER_SEGMENT = 24;

    private void Start()
    {
        RebuildTrackCache();
    }

    private void OnValidate()
    {
        // Keep cached lengths in sync while editing
        RebuildTrackCache();
    }

    public new void StartMoving()
    {
        if (track == null || track.Count < 2) return;
        _moving = true;
        _waiting = false;
        _currentSpeed = Mathf.Max(_currentSpeed, Mathf.Min(baseSpeed, maxSpeed) * 0.5f);
        EnsureValidSegment();
    }

    public new void StopMoving()
    {
        _moving = false;
        _waiting = false;
        _currentSpeed = 0f;
    }

    public new void ReverseDirection()
    {
        _direction = -_direction;
    }

    public void SetTrackFromTransforms(IList<Transform> controlPoints, bool useAsReferences = true)
    {
        track.Clear();
        if (controlPoints == null || controlPoints.Count == 0) return;
        for (int i = 0; i < controlPoints.Count; i++)
        {
            var t = controlPoints[i];
            var p = new TrackPoint
            {
                position = t.position,
                referenceObject = useAsReferences ? t.gameObject : null,
                waitTime = 0f,
                speedLimit = 1000f,
                rollDegrees = 0f
            };
            track.Add(p);
        }
        RebuildTrackCache();
    }

    // IMoverController implementation (shadow the base one so PhysicsMover calls this)
    public new void UpdateMovement(out Vector3 goalPosition, out Quaternion goalRotation, float deltaTime)
    {
        goalPosition = transform.position;
        goalRotation = transform.rotation;

        if (!_moving || track == null || track.Count < 2)
        {
            return;
        }

        if (_waiting)
        {
            if (_waitCounter > 0f)
            {
                _waitCounter -= deltaTime;
            }
            else
            {
                _waiting = false;
            }
            return;
        }

        // Advance speed towards target for this segment
        float targetSpeed = Mathf.Min(baseSpeed, GetCurrentSegmentSpeedLimit(), maxSpeed);
        if (_currentSpeed < targetSpeed)
        {
            _currentSpeed = Mathf.Min(targetSpeed, _currentSpeed + acceleration * deltaTime);
        }
        else if (_currentSpeed > targetSpeed)
        {
            _currentSpeed = Mathf.Max(targetSpeed, _currentSpeed - deceleration * deltaTime);
        }

        float advance = _currentSpeed * deltaTime;
        _distanceOnSegment += advance;

        while (_distanceOnSegment >= _segmentLength - 0.0001f)
        {
            // Arrive at next point
            _distanceOnSegment -= _segmentLength;
            int arrivedIndex = NextIndex(_segmentIndex, _direction);

            // Handle wait at station/point
            float wait = Mathf.Max(0f, track[arrivedIndex].waitTime);
            if (wait > 0f)
            {
                _waiting = true;
                _waitCounter = wait;
                // Snap to exact point
                goalPosition = track[arrivedIndex].referenceObject != null ? track[arrivedIndex].referenceObject.transform.position : track[arrivedIndex].position;
                ApplyPose(goalPosition, arrivedIndex, 0f, out goalRotation);
                // Prepare next run starting from this point
                _segmentIndex = arrivedIndex;
                _segmentLength = ComputeSegmentLength(_segmentIndex);
                _distanceOnSegment = 0f;
                UpdateCarsAlongTrack(NormalizedDistanceFromStart());
                return;
            }

            // Move to next segment
            _segmentIndex = arrivedIndex;
            _segmentLength = ComputeSegmentLength(_segmentIndex);

            // If non-looping and at ends, stop
            if (!loopTrack)
            {
                if ((_segmentIndex == 0 && _direction < 0) || (_segmentIndex == track.Count - 2 && _direction > 0))
                {
                    StopMoving();
                    return;
                }
            }
        }

        float t = _segmentLength > 0.0001f ? Mathf.Clamp01(_distanceOnSegment / _segmentLength) : 0f;
        Vector3 pos = EvaluatePositionOnSegment(_segmentIndex, t);
        goalPosition = pos;
        ApplyPose(pos, _segmentIndex, t, out goalRotation);

        UpdateCarsAlongTrack(NormalizedDistanceFromStart());
    }

    private void ApplyPose(Vector3 position, int segmentIndex, float t, out Quaternion rotation)
    {
        if (!alignToTangent)
        {
            rotation = transform.rotation;
            return;
        }

        Vector3 tangent = EvaluateTangentOnSegment(segmentIndex, Mathf.Clamp01(t));
        if (tangent.sqrMagnitude < 1e-6f)
        {
            rotation = transform.rotation;
            return;
        }

        Quaternion look = Quaternion.LookRotation(tangent.normalized, worldUp);

        float rollA = track[segmentIndex].rollDegrees;
        int nextIdx = NextIndex(segmentIndex, _direction);
        float rollB = track[nextIdx].rollDegrees;
        float rollLerp = Mathf.Lerp(rollA, rollB, t);

        float curvatureBank = 0f;
        if (bankFromCurvature)
        {
            Vector3 t0 = tangent.normalized;
            Vector3 t1 = EvaluateTangentOnSegment(segmentIndex, Mathf.Clamp01(t + 0.05f)).normalized;
            float signed = SignedAngleAroundAxis(t0, t1, worldUp);
            curvatureBank = Mathf.Clamp(signed * bankingMultiplier, -bankingMaxDegrees, bankingMaxDegrees);
        }

        float targetBank = Mathf.Clamp(curvatureBank + rollLerp, -bankingMaxDegrees, bankingMaxDegrees);
        _smoothedBankDeg = Mathf.Lerp(_smoothedBankDeg, targetBank, Time.deltaTime * bankSmoothing);

        rotation = Quaternion.AngleAxis(_smoothedBankDeg, tangent.normalized) * look;
    }

    private float SignedAngleAroundAxis(Vector3 from, Vector3 to, Vector3 axis)
    {
        Vector3 projFrom = Vector3.ProjectOnPlane(from, axis);
        Vector3 projTo = Vector3.ProjectOnPlane(to, axis);
        float angle = Vector3.SignedAngle(projFrom, projTo, axis);
        return angle;
    }

    private void UpdateCarsAlongTrack(float headNormalized)
    {
        if (cars == null || cars.Count == 0 || _totalLength <= 0.0001f) return;
        for (int i = 0; i < cars.Count; i++)
        {
            var car = cars[i];
            if (car == null) continue;
            float offsetMeters = carSpacingMeters * (i + 1);
            float normOffset = offsetMeters / _totalLength;
            float carNorm = headNormalized - normOffset * _direction;
            if (loopTrack)
            {
                carNorm = Mathf.Repeat(carNorm, 1f);
            }
            else
            {
                carNorm = Mathf.Clamp01(carNorm);
            }
            EvaluateAtNormalized(carNorm, out Vector3 pos, out Quaternion rot);
            car.SetPositionAndRotation(pos, rot);
        }
    }

    private float GetCurrentSegmentSpeedLimit()
    {
        int nextIdx = NextIndex(_segmentIndex, _direction);
        float limA = Mathf.Max(0.1f, track[_segmentIndex].speedLimit);
        float limB = Mathf.Max(0.1f, track[nextIdx].speedLimit);
        return Mathf.Min(limA, limB);
    }

    private int NextIndex(int i, int dir)
    {
        if (dir >= 0)
        {
            if (i >= track.Count - 1) return loopTrack ? 0 : track.Count - 1;
            return i + 1;
        }
        else
        {
            if (i <= 0) return loopTrack ? track.Count - 1 : 0;
            return i - 1;
        }
    }

    private void EnsureValidSegment()
    {
        _segmentIndex = Mathf.Clamp(_segmentIndex, 0, Math.Max(0, track.Count - 2));
        _segmentLength = ComputeSegmentLength(_segmentIndex);
        _distanceOnSegment = Mathf.Clamp(_distanceOnSegment, 0f, _segmentLength);
    }

    private Vector3 EvaluatePositionOnSegment(int i, float t)
    {
        t = Mathf.Clamp01(t);
        int j = NextIndex(i, +1);
        Vector3 p0 = GetPointPosition(Mathf.Clamp(i - 1, 0, track.Count - 1));
        Vector3 p1 = GetPointPosition(i);
        Vector3 p2 = GetPointPosition(j);
        Vector3 p3 = GetPointPosition(Mathf.Clamp(j + 1, 0, track.Count - 1));

        if (pathMode == PathMode.Linear)
        {
            return Vector3.Lerp(p1, p2, t);
        }
        else if (pathMode == PathMode.CatmullRom)
        {
            return CatmullRom(p0, p1, p2, p3, t);
        }
        else
        {
            // Bezier
            GetBezierSegment(i, out Vector3 b0, out Vector3 b1, out Vector3 b2, out Vector3 b3);
            return Bezier(b0, b1, b2, b3, t);
        }
    }

    private Vector3 EvaluateTangentOnSegment(int i, float t)
    {
        t = Mathf.Clamp01(t);
        int j = NextIndex(i, +1);
        Vector3 p0 = GetPointPosition(Mathf.Clamp(i - 1, 0, track.Count - 1));
        Vector3 p1 = GetPointPosition(i);
        Vector3 p2 = GetPointPosition(j);
        Vector3 p3 = GetPointPosition(Mathf.Clamp(j + 1, 0, track.Count - 1));

        if (pathMode == PathMode.Linear)
        {
            return (p2 - p1);
        }
        else if (pathMode == PathMode.CatmullRom)
        {
            return CatmullRomTangent(p0, p1, p2, p3, t);
        }
        else
        {
            GetBezierSegment(i, out Vector3 b0, out Vector3 b1, out Vector3 b2, out Vector3 b3);
            return BezierTangent(b0, b1, b2, b3, t);
        }
    }

    private void GetBezierSegment(int i, out Vector3 b0, out Vector3 b1, out Vector3 b2, out Vector3 b3)
    {
        int j = NextIndex(i, +1);
        Vector3 pPrev = GetPointPosition(Mathf.Clamp(i - 1, 0, track.Count - 1));
        Vector3 p0 = GetPointPosition(i);
        Vector3 p1 = GetPointPosition(j);
        Vector3 pNext = GetPointPosition(Mathf.Clamp(j + 1, 0, track.Count - 1));

        b0 = p0;
        b3 = p1;

        Vector3 outH, inH;
        if (autoBezierHandles && !track[i].useCustomHandles && !track[j].useCustomHandles)
        {
            Vector3 t0 = (p1 - pPrev).normalized;
            Vector3 t1 = (pNext - p0).normalized;
            float d01 = Vector3.Distance(p0, p1);
            outH = p0 + t0 * d01 * bezierTension * 0.5f;
            inH = p1 - t1 * d01 * bezierTension * 0.5f;
        }
        else
        {
            outH = p0 + track[i].handleOut;
            inH = p1 + track[j].handleIn;
        }
        b1 = outH;
        b2 = inH;
    }

    private Vector3 GetPointPosition(int index)
    {
        index = Mathf.Clamp(index, 0, track.Count - 1);
        var tp = track[index];
        if (tp.referenceObject != null)
        {
            return tp.referenceObject.transform.position;
        }
        return tp.position;
    }

    private float ComputeSegmentLength(int i)
    {
        float len = 0f;
        Vector3 prev = EvaluatePositionOnSegment(i, 0f);
        for (int s = 1; s <= SAMPLES_PER_SEGMENT; s++)
        {
            float t = (float)s / SAMPLES_PER_SEGMENT;
            Vector3 pt = EvaluatePositionOnSegment(i, t);
            len += Vector3.Distance(prev, pt);
            prev = pt;
        }
        return Mathf.Max(0.0001f, len);
    }

    private void RebuildTrackCache()
    {
        _segmentLengths.Clear();
        _cumulativeLengths.Clear();
        _totalLength = 0f;

        if (track == null)
            return;

        for (int i = 0; i < track.Count; i++)
        {
            track[i]?.UpdateFromReference();
        }

        if (track.Count < 2)
            return;

        int segmentCount = loopTrack ? track.Count : track.Count - 1;
        for (int i = 0; i < segmentCount; i++)
        {
            float len = ComputeSegmentLength(i);
            _segmentLengths.Add(len);
            _totalLength += len;
            _cumulativeLengths.Add(_totalLength);
        }

        EnsureValidSegment();
    }

    private float NormalizedDistanceFromStart()
    {
        if (_totalLength <= 0.0001f) return 0f;
        float before = _segmentIndex > 0 ? _cumulativeLengths[_segmentIndex - 1] : 0f;
        float d = before + _distanceOnSegment;
        return Mathf.Clamp01(d / _totalLength);
    }

    private void EvaluateAtNormalized(float normalized, out Vector3 pos, out Quaternion rot)
    {
        normalized = Mathf.Clamp01(normalized);
        if (_totalLength <= 0.0001f || _segmentLengths.Count == 0)
        {
            pos = transform.position;
            rot = transform.rotation;
            return;
        }

        float targetDist = normalized * _totalLength;
        int seg = 0;
        float prevCum = 0f;
        for (int i = 0; i < _cumulativeLengths.Count; i++)
        {
            if (targetDist <= _cumulativeLengths[i])
            {
                seg = i;
                break;
            }
            prevCum = _cumulativeLengths[i];
            seg = i;
        }
        float segLen = _segmentLengths[Mathf.Clamp(seg, 0, _segmentLengths.Count - 1)];
        float localD = Mathf.Clamp(targetDist - prevCum, 0f, segLen);
        float t = segLen > 0.0001f ? localD / segLen : 0f;
        pos = EvaluatePositionOnSegment(seg, t);

        Vector3 tangent = EvaluateTangentOnSegment(seg, t);
        Quaternion look = tangent.sqrMagnitude > 1e-6f ? Quaternion.LookRotation(tangent.normalized, worldUp) : transform.rotation;
        rot = look;
    }

    // Curve math
    private Vector3 CatmullRom(Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3, float t)
    {
        float t2 = t * t;
        float t3 = t2 * t;
        return 0.5f * (
            (2f * p1) +
            (-p0 + p2) * t +
            (2f * p0 - 5f * p1 + 4f * p2 - p3) * t2 +
            (-p0 + 3f * p1 - 3f * p2 + p3) * t3
        );
    }

    private Vector3 CatmullRomTangent(Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3, float t)
    {
        float t2 = t * t;
        return 0.5f * (
            (-p0 + p2) +
            2f * (2f * p0 - 5f * p1 + 4f * p2 - p3) * t +
            3f * (-p0 + 3f * p1 - 3f * p2 + p3) * t2
        );
    }

    private Vector3 Bezier(Vector3 b0, Vector3 b1, Vector3 b2, Vector3 b3, float t)
    {
        float u = 1f - t;
        return u * u * u * b0 + 3f * u * u * t * b1 + 3f * u * t * t * b2 + t * t * t * b3;
    }

    private Vector3 BezierTangent(Vector3 b0, Vector3 b1, Vector3 b2, Vector3 b3, float t)
    {
        float u = 1f - t;
        return 3f * u * u * (b1 - b0) + 6f * u * t * (b2 - b1) + 3f * t * t * (b3 - b2);
    }

    // Gizmos
    private void OnDrawGizmosSelected()
    {
        if (track == null || track.Count < 2) return;
        // Draw control points
        Gizmos.color = Color.cyan;
        for (int i = 0; i < track.Count; i++)
        {
            var tp = track[i];
            Vector3 p = tp.referenceObject != null ? tp.referenceObject.transform.position : tp.position;
            Gizmos.DrawWireSphere(p, 0.15f);
        }
        // Draw curve
        Gizmos.color = Color.green;
        int segCount = loopTrack ? track.Count : track.Count - 1;
        for (int i = 0; i < segCount; i++)
        {
            Vector3 prev = EvaluatePositionOnSegment(i, 0f);
            for (int s = 1; s <= 40; s++)
            {
                float t = s / 40f;
                Vector3 pt = EvaluatePositionOnSegment(i, t);
                Gizmos.DrawLine(prev, pt);
                prev = pt;
            }
        }
    }
}


